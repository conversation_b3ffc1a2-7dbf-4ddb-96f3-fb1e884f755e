import 'dart:math';
import '../models/colis.dart';
import '../services/colis_service.dart';

/// Script pour ajouter des livraisons de test pour démonstration
class TestDeliveriesGenerator {
  static final ColisService _colisService = ColisService();
  static final Random _random = Random();

  /// Zones de livraison disponibles
  static const List<String> _zones = [
    'Cocody',
    'Plateau',
    'Marcory',
    'Treichville',
    'Adjamé',
    'Yopougon',
    'Abobo',
    'Port Bouet',
    'Koumassi',
    'Bingerville',
  ];

  /// Noms de clients de test
  static const List<String> _clientNames = [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    'Doumbia Aminata',
    '<PERSON><PERSON> David',
    'Coulibaly Mariam',
  ];

  /// Libellés de produits de test
  static const List<String> _productLabels = [
    'Colis Électronique',
    'Produits Cosmétiques',
    'Vêtements Mode',
    'Accessoires Tech',
    'Articles Maison',
    'Produits Alimentaires',
    'Matériel Bureau',
    'Jouets Enfants',
    'Livres et Magazines',
    'Équipement Sport',
  ];

  /// Générer des livraisons de test pour la semaine en cours
  static Future<void> generateWeeklyTestDeliveries() async {
    print('🚀 Génération des livraisons de test pour la semaine...');
    
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    
    // Générer 15-25 livraisons pour la semaine
    final deliveryCount = 15 + _random.nextInt(11);
    
    for (int i = 0; i < deliveryCount; i++) {
      // Date aléatoire dans la semaine
      final dayOffset = _random.nextInt(7);
      final hourOffset = _random.nextInt(24);
      final deliveryDate = startOfWeek.add(Duration(days: dayOffset, hours: hourOffset));
      
      await _createTestDelivery(deliveryDate);
    }
    
    print('✅ $deliveryCount livraisons de test créées pour la semaine');
  }

  /// Générer des livraisons de test pour le mois en cours
  static Future<void> generateMonthlyTestDeliveries() async {
    print('🚀 Génération des livraisons de test pour le mois...');
    
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    
    // Générer 40-80 livraisons pour le mois
    final deliveryCount = 40 + _random.nextInt(41);
    
    for (int i = 0; i < deliveryCount; i++) {
      // Date aléatoire dans le mois
      final dayOffset = _random.nextInt(daysInMonth);
      final hourOffset = _random.nextInt(24);
      final deliveryDate = startOfMonth.add(Duration(days: dayOffset, hours: hourOffset));
      
      await _createTestDelivery(deliveryDate);
    }
    
    print('✅ $deliveryCount livraisons de test créées pour le mois');
  }

  /// Créer une livraison de test avec des données aléatoires
  static Future<void> _createTestDelivery(DateTime date) async {
    final zone = _zones[_random.nextInt(_zones.length)];
    final clientName = _clientNames[_random.nextInt(_clientNames.length)];
    final productLabel = _productLabels[_random.nextInt(_productLabels.length)];
    
    // Générer un numéro de téléphone aléatoire
    final phoneNumber = '0${_random.nextInt(9) + 1}${_random.nextInt(10)}${_random.nextInt(10)}${_random.nextInt(10)}${_random.nextInt(10)}${_random.nextInt(10)}${_random.nextInt(10)}${_random.nextInt(10)}';
    
    // Statut aléatoire avec probabilités réalistes
    final statusRandom = _random.nextDouble();
    StatutLivraison status;
    if (statusRandom < 0.6) {
      status = StatutLivraison.livree; // 60% livrées
    } else if (statusRandom < 0.8) {
      status = StatutLivraison.enCours; // 20% en cours
    } else if (statusRandom < 0.9) {
      status = StatutLivraison.enRetard; // 10% en retard
    } else if (statusRandom < 0.95) {
      status = StatutLivraison.annulee; // 5% annulées
    } else {
      status = StatutLivraison.retour; // 5% retours
    }
    
    // Prix de livraison selon la zone
    final deliveryPrice = ZoneLivraison.getPrixLivraison(zone);
    
    // Montant restant à payer (0 si livré, aléatoire sinon)
    final remainingAmount = status == StatutLivraison.livree 
        ? 0.0 
        : (_random.nextDouble() * 50000).roundToDouble();
    
    // Créer le colis
    final colis = Colis(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(1000)}',
      libelle: '$productLabel - ${clientName.split(' ')[0]}',
      photoPath: 'assets/images/default_product.png', // Image par défaut
      zoneLivraison: zone,
      numeroClient: phoneNumber,
      resteAPayer: remainingAmount,
      fraisLivraison: deliveryPrice,
      dateAjout: date,
      statut: status,
      nomClient: clientName,
      adresseLivraison: _generateRandomAddress(zone),
      notes: _generateRandomNotes(),
    );
    
    // Ajouter le colis via le service
    await _colisService.addColis(colis);
  }

  /// Générer une adresse aléatoire pour une zone
  static String _generateRandomAddress(String zone) {
    final streets = [
      'Rue des Jardins',
      'Avenue de la Paix',
      'Boulevard du Commerce',
      'Rue de l\'Indépendance',
      'Avenue des Palmiers',
      'Rue de la République',
      'Boulevard de la Marina',
      'Avenue du Plateau',
    ];
    
    final street = streets[_random.nextInt(streets.length)];
    final number = _random.nextInt(200) + 1;
    
    return '$number $street, $zone';
  }

  /// Générer des notes aléatoires (optionnelles)
  static String? _generateRandomNotes() {
    final notesOptions = [
      null, // 40% sans notes
      null,
      'Client préfère livraison le matin',
      'Appeler avant livraison',
      'Laisser chez le gardien si absent',
      'Fragile - manipuler avec précaution',
      'Livraison urgente',
      'Client difficile à joindre',
      'Adresse difficile à trouver',
      'Paiement à la livraison',
    ];
    
    return notesOptions[_random.nextInt(notesOptions.length)];
  }

  /// Nettoyer toutes les livraisons de test
  static Future<void> clearTestDeliveries() async {
    print('🧹 Suppression des livraisons de test...');
    
    final allColis = await _colisService.loadColis();
    final testColis = allColis.where((colis) => colis.id.startsWith('test_')).toList();
    
    // Supprimer les colis de test
    final remainingColis = allColis.where((colis) => !colis.id.startsWith('test_')).toList();
    await _colisService.saveColis(remainingColis);
    
    print('✅ ${testColis.length} livraisons de test supprimées');
  }

  /// Générer un ensemble complet de données de test
  static Future<void> generateCompleteTestData() async {
    print('🎯 Génération complète des données de test...');
    
    // Nettoyer d'abord les anciennes données de test
    await clearTestDeliveries();
    
    // Générer les nouvelles données
    await generateWeeklyTestDeliveries();
    await generateMonthlyTestDeliveries();
    
    print('🎉 Génération complète terminée !');
  }
}
