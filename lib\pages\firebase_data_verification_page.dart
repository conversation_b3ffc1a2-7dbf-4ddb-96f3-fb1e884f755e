import 'package:flutter/material.dart';
import '../services/firebase_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirebaseDataVerificationPage extends StatefulWidget {
  const FirebaseDataVerificationPage({super.key});

  @override
  State<FirebaseDataVerificationPage> createState() =>
      _FirebaseDataVerificationPageState();
}

class _FirebaseDataVerificationPageState
    extends State<FirebaseDataVerificationPage> {
  bool _isLoading = false;
  Map<String, dynamic> _verificationResults = {};
  String _statusMessage = 'Prêt à vérifier';

  @override
  void initState() {
    super.initState();
    _performVerification();
  }

  Future<void> _performVerification() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Vérification en cours...';
    });

    try {
      // Initialiser Firebase
      await FirebaseService.instance.initialize();

      if (!FirebaseService.instance.isOnline()) {
        setState(() {
          _statusMessage = 'Firebase hors ligne';
          _isLoading = false;
        });
        return;
      }

      final results = <String, dynamic>{};

      // Vérifier les factures
      final invoiceResults = await _verifyInvoices();
      results['invoices'] = invoiceResults;

      // Vérifier les colis
      final colisResults = await _verifyColis();
      results['colis'] = colisResults;

      // Vérifier les tâches
      final taskResults = await _verifyTasks();
      results['tasks'] = taskResults;

      // Vérifier les produits
      final productResults = await _verifyProducts();
      results['products'] = productResults;

      setState(() {
        _verificationResults = results;
        _statusMessage = 'Vérification terminée';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur: $e';
        _isLoading = false;
      });
    }
  }

  Future<Map<String, dynamic>> _verifyInvoices() async {
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localInvoicesJson = prefs.getStringList('invoices') ?? [];

      // Données Firebase
      final firebaseInvoices = await FirebaseService.instance.getAllInvoices();

      return {
        'local_count': localInvoicesJson.length,
        'firebase_count': firebaseInvoices.length,
        'synced': localInvoicesJson.length == firebaseInvoices.length,
        'firebase_data':
            firebaseInvoices
                .take(3)
                .map(
                  (i) => {
                    'id': i.id,
                    'total': i.total,
                    'status': i.status.toString(),
                    'date': i.createdAt.toIso8601String(),
                  },
                )
                .toList(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _verifyColis() async {
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localColisJson = prefs.getStringList('colis') ?? [];

      // Données Firebase
      final firebaseColis = await FirebaseService.instance.getAllColis();

      return {
        'local_count': localColisJson.length,
        'firebase_count': firebaseColis.length,
        'synced': localColisJson.length == firebaseColis.length,
        'firebase_data':
            firebaseColis
                .take(3)
                .map(
                  (c) => {
                    'id': c.id,
                    'statut': c.statut,
                    'reste_a_payer': c.resteAPayer,
                    'date': c.dateAjout.toIso8601String(),
                  },
                )
                .toList(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _verifyTasks() async {
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localTasksJson = prefs.getStringList('tasks') ?? [];

      // Données Firebase
      final firebaseTasks = await FirebaseService.instance.getAllTasks();

      return {
        'local_count': localTasksJson.length,
        'firebase_count': firebaseTasks.length,
        'synced': localTasksJson.length == firebaseTasks.length,
        'firebase_data':
            firebaseTasks
                .take(3)
                .map(
                  (t) => {
                    'id': t.id,
                    'title': t.title,
                    'completed': t.isCompleted,
                    'date': t.createdAt.toIso8601String(),
                  },
                )
                .toList(),
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _verifyProducts() async {
    try {
      // Données locales
      final prefs = await SharedPreferences.getInstance();
      final localProductsJson = prefs.getStringList('products') ?? [];

      // Note: Les produits ne sont PAS synchronisés avec Firebase
      return {
        'local_count': localProductsJson.length,
        'firebase_count': 0,
        'synced':
            true, // Considéré comme synchronisé car ils ne doivent pas être dans Firebase
        'note': 'Les produits restent locaux (non synchronisés avec Firebase)',
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vérification Firebase'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _performVerification,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Vérification en cours...'),
                  ],
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatusCard(),
                    const SizedBox(height: 16),
                    if (_verificationResults.isNotEmpty) ..._buildResultCards(),
                  ],
                ),
              ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLoading
                      ? Icons.hourglass_empty
                      : _statusMessage.contains('Erreur')
                      ? Icons.error
                      : Icons.check_circle,
                  color:
                      _isLoading
                          ? Colors.orange
                          : _statusMessage.contains('Erreur')
                          ? Colors.red
                          : Colors.green,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Statut de la vérification',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(_statusMessage),
            const SizedBox(height: 8),
            Text(
              'Firebase en ligne: ${FirebaseService.instance.isOnline() ? "Oui" : "Non"}',
              style: TextStyle(
                color:
                    FirebaseService.instance.isOnline()
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildResultCards() {
    final cards = <Widget>[];

    _verificationResults.forEach((key, value) {
      cards.add(_buildDataTypeCard(key, value));
      cards.add(const SizedBox(height: 12));
    });

    return cards;
  }

  Widget _buildDataTypeCard(String dataType, Map<String, dynamic> data) {
    final hasError = data.containsKey('error');
    final isSynced = data['synced'] ?? false;

    String title;
    IconData icon;
    switch (dataType) {
      case 'invoices':
        title = 'Factures';
        icon = Icons.receipt;
        break;
      case 'colis':
        title = 'Colis';
        icon = Icons.local_shipping;
        break;
      case 'tasks':
        title = 'Tâches';
        icon = Icons.task;
        break;
      case 'products':
        title = 'Produits';
        icon = Icons.inventory;
        break;
      default:
        title = dataType;
        icon = Icons.data_object;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Icon(
                  hasError
                      ? Icons.error
                      : isSynced
                      ? Icons.check_circle
                      : Icons.warning,
                  color:
                      hasError
                          ? Colors.red
                          : isSynced
                          ? Colors.green
                          : Colors.orange,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (hasError)
              Text(
                'Erreur: ${data['error']}',
                style: const TextStyle(color: Colors.red),
              )
            else
              ..._buildDataDetails(data),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDataDetails(Map<String, dynamic> data) {
    final widgets = <Widget>[];

    // Compteurs
    if (data.containsKey('local_count')) {
      widgets.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Données locales:'),
            Text(
              '${data['local_count']}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );
    }

    if (data.containsKey('firebase_count')) {
      widgets.add(
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Données Firebase:'),
            Text(
              '${data['firebase_count']}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );
    }

    // Statut de synchronisation
    if (data.containsKey('synced')) {
      widgets.add(const SizedBox(height: 8));
      widgets.add(
        Row(
          children: [
            Icon(
              data['synced'] ? Icons.sync : Icons.sync_problem,
              color: data['synced'] ? Colors.green : Colors.orange,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              data['synced'] ? 'Synchronisé' : 'Non synchronisé',
              style: TextStyle(
                color: data['synced'] ? Colors.green : Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }

    // Note spéciale
    if (data.containsKey('note')) {
      widgets.add(const SizedBox(height: 8));
      widgets.add(
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            data['note'],
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
        ),
      );
    }

    // Exemples de données Firebase
    if (data.containsKey('firebase_data') && data['firebase_data'] is List) {
      final firebaseData = data['firebase_data'] as List;
      if (firebaseData.isNotEmpty) {
        widgets.add(const SizedBox(height: 12));
        widgets.add(
          const Text(
            'Exemples de données Firebase:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        );
        widgets.add(const SizedBox(height: 4));

        for (final item in firebaseData) {
          widgets.add(
            Container(
              margin: const EdgeInsets.only(bottom: 4),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                item.toString(),
                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ),
          );
        }
      }
    }

    return widgets;
  }
}
