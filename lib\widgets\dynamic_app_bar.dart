import 'package:flutter/material.dart';
import '../services/theme_service.dart';

/// AppBar dynamique qui change de couleur selon la page
class DynamicAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String pageKey;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final PreferredSizeWidget? bottom;
  final double? elevation;
  final bool centerTitle;

  const DynamicAppBar({
    super.key,
    required this.title,
    required this.pageKey,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.bottom,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ThemeService.instance.getThemeForPage(pageKey);
    
    // Appliquer le thème système
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ThemeService.instance.applySystemUITheme(theme);
    });

    return AppBar(
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icône de la page
          _getPageIcon(pageKey),
          const SizedBox(width: 8),
          // Titre avec animation
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 300),
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
            child: Text(title),
          ),
        ],
      ),
      backgroundColor: theme.primary,
      foregroundColor: Colors.white,
      elevation: elevation ?? 4,
      centerTitle: centerTitle,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
      bottom: bottom,
      shadowColor: theme.secondary.withOpacity(0.5),
      
      // Gradient en arrière-plan
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.primary,
              theme.secondary,
            ],
          ),
        ),
      ),
    );
  }

  /// Obtenir l'icône correspondant à la page
  Widget _getPageIcon(String pageKey) {
    final iconData = _getIconData(pageKey);
    final theme = ThemeService.instance.getThemeForPage(pageKey);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        iconData,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  /// Mapper les clés de page aux icônes
  IconData _getIconData(String pageKey) {
    switch (pageKey) {
      case 'home':
        return Icons.home;
      case 'invoices':
        return Icons.receipt_long;
      case 'products':
        return Icons.inventory_2;
      case 'tasks':
        return Icons.task_alt;
      case 'colis':
        return Icons.local_shipping;
      case 'clients':
        return Icons.people;
      case 'reports':
        return Icons.analytics;
      case 'settings':
        return Icons.settings;
      case 'backup':
        return Icons.backup;
      case 'diagnostic':
        return Icons.bug_report;
      case 'gamification':
        return Icons.emoji_events;
      case 'profile':
        return Icons.person;
      default:
        return Icons.apps;
    }
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
  );
}

/// Bottom Navigation Bar dynamique
class DynamicBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final String currentPageKey;

  const DynamicBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.currentPageKey,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ThemeService.instance.getThemeForPage(currentPageKey);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.primary,
            theme.secondary,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: theme.secondary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        backgroundColor: Colors.transparent,
        elevation: 0,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.white,
        unselectedItemColor: Colors.white70,
        selectedFontSize: 12,
        unselectedFontSize: 10,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Accueil',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_long),
            label: 'Factures',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2),
            label: 'Produits',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.task_alt),
            label: 'Tâches',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.more_horiz),
            label: 'Plus',
          ),
        ],
      ),
    );
  }
}

/// Floating Action Button dynamique
class DynamicFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final String pageKey;
  final String? tooltip;

  const DynamicFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.child,
    required this.pageKey,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ThemeService.instance.getThemeForPage(pageKey);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        backgroundColor: theme.accent,
        foregroundColor: Colors.white,
        elevation: 6,
        highlightElevation: 12,
        child: child,
      ),
    );
  }
}

/// Scaffold avec thème dynamique
class DynamicScaffold extends StatelessWidget {
  final String pageKey;
  final String title;
  final Widget body;
  final List<Widget>? appBarActions;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final bool extendBodyBehindAppBar;
  final bool extendBody;

  const DynamicScaffold({
    super.key,
    required this.pageKey,
    required this.title,
    required this.body,
    this.appBarActions,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
    this.extendBodyBehindAppBar = false,
    this.extendBody = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ThemeService.instance.getThemeForPage(pageKey);

    return Theme(
      data: ThemeService.instance.createPageTheme(theme),
      child: Scaffold(
        appBar: DynamicAppBar(
          title: title,
          pageKey: pageKey,
          actions: appBarActions,
        ),
        body: body,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
        drawer: drawer,
        endDrawer: endDrawer,
        extendBodyBehindAppBar: extendBodyBehindAppBar,
        extendBody: extendBody,
        backgroundColor: Colors.grey[50],
      ),
    );
  }
}
