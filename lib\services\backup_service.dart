import 'dart:convert';
import 'package:flutter/foundation.dart' hide Category;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Platform-specific imports
import 'dart:io' if (dart.library.io) 'dart:io';
import 'html_stub.dart' if (dart.library.js) 'dart:html' as html;
import '../models/product.dart';
import '../models/category.dart';
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';
import 'data_change_notifier.dart';
import 'backup_optimization_service.dart';
import 'segmented_backup_service.dart';
import 'package:path/path.dart' as path;

class BackupService {
  static const String _backupVersion = '2.0';

  /// Créer une sauvegarde optimisée (recommandé pour de gros volumes)
  static Future<Map<String, dynamic>> createOptimizedBackup({
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      onProgress?.call(0.0, 'Estimation de la taille...');

      // Estimer la taille de la sauvegarde
      final sizeEstimate = await BackupOptimizationService.estimateBackupSize();
      final totalSize = sizeEstimate['total'] ?? 0;

      debugPrint('📊 Taille estimée de la sauvegarde: $totalSize caractères');

      // Utiliser toujours la sauvegarde optimisée
      debugPrint('📦 Utilisation de la sauvegarde optimisée');
      return await BackupOptimizationService.createOptimizedBackup(
        onProgress: onProgress,
      );
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde optimisée: $e');
      // Fallback vers la sauvegarde standard
      return await createBackup();
    }
  }

  /// Crée une sauvegarde complète de toutes les données de l'application (méthode standard)
  static Future<Map<String, dynamic>> createBackup() async {
    try {
      // Récupérer toutes les données
      final products = await InventoryService.instance.getProducts();
      final categories = await InventoryService.instance.getCategories();
      final invoices =
          await InvoiceService.loadInvoices(); // Keep using static method as it's still static
      final tasks = await TaskService.instance.getTasks();
      final colis = await ColisService.instance.loadColis();

      // Sauvegarder les images des factures et des colis
      final invoiceImages = await _backupInvoiceImages(invoices);
      final colisImages = await _backupColisImages(colis);

      // Créer la structure de sauvegarde
      final backup = {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'data': {
          'products': products.map((p) => p.toJson()).toList(),
          'categories': categories.map((c) => c.toJson()).toList(),
          'invoices': invoices.map((i) => i.toJson()).toList(),
          'tasks': tasks.map((t) => t.toJson()).toList(),
          'colis': colis.map((c) => c.toJson()).toList(),
        },
        'images': {'invoices': invoiceImages, 'colis': colisImages},
      };

      return backup;
    } catch (e) {
      throw Exception('Erreur lors de la création de la sauvegarde: $e');
    }
  }

  /// Exporte la sauvegarde vers un fichier JSON
  static Future<String> exportBackupToFile() async {
    try {
      final backup = await createBackup();
      final jsonString = jsonEncode(backup);

      if (kIsWeb) {
        // Sur le web, on ne peut pas créer de fichier physique
        // Retourner un identifiant spécial
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        return 'web_backup_$timestamp.json';
      } else {
        // Sur les plateformes natives
        final directory = await getApplicationDocumentsDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = 'hcp_backup_$timestamp.json';
        final file = File('${directory.path}/$fileName');

        // Écrire le fichier
        await file.writeAsString(jsonString);

        return file.path;
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'exportation: $e');
    }
  }

  /// Télécharge la sauvegarde pour le web
  static Future<void> downloadBackupForWeb() async {
    try {
      final backup = await createBackup();
      final jsonString = jsonEncode(backup);

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'hcp_backup_$timestamp.json';

      // Utiliser html pour télécharger le fichier sur le web
      if (kIsWeb) {
        // Créer un blob avec le contenu JSON
        final blob = html.Blob([jsonString], 'application/json');
        final url = html.Url.createObjectUrl(blob);
        final anchor = html.AnchorElement(href: url)..download = fileName;
        html.document.body!.append(anchor);
        anchor.click();
        anchor.remove();
        html.Url.revokeObjectUrl(url);
      }
    } catch (e) {
      throw Exception('Erreur lors du téléchargement: $e');
    }
  }

  /// Partage la sauvegarde via le système de partage natif
  static Future<void> shareBackup() async {
    try {
      if (kIsWeb) {
        // Sur le web, télécharger directement le fichier
        await downloadBackupForWeb();
      } else {
        // Sur les plateformes natives
        final filePath = await exportBackupToFile();
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'Sauvegarde HCP-DESIGN Analytics',
          subject: 'Sauvegarde des données',
        );
      }
    } catch (e) {
      throw Exception('Erreur lors du partage: $e');
    }
  }

  /// Permet à l'utilisateur de sélectionner un fichier de sauvegarde
  static Future<String?> selectBackupFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'Sélectionner un fichier de sauvegarde',
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;

        if (kIsWeb) {
          // Sur le web, on utilise les bytes directement
          if (file.bytes != null) {
            // Créer un fichier temporaire avec les bytes pour la compatibilité
            final jsonString = String.fromCharCodes(file.bytes!);
            // Valider que c'est du JSON valide
            try {
              jsonDecode(jsonString);
              // Retourner un identifiant spécial pour le web
              return 'web_file_${file.name}';
            } catch (e) {
              throw Exception('Fichier JSON invalide');
            }
          }
        } else {
          // Sur les plateformes natives, utiliser le path
          if (file.path != null) {
            return file.path!;
          }
        }
      }

      return null;
    } catch (e) {
      throw Exception('Erreur lors de la sélection du fichier: $e');
    }
  }

  /// Restaure les données à partir d'un fichier de sauvegarde
  static Future<void> restoreFromFile(String filePath) async {
    try {
      String jsonString;

      if (kIsWeb && filePath.startsWith('web_file_')) {
        // Sur le web, on doit re-sélectionner le fichier car on ne peut pas stocker les bytes
        throw Exception(
          'Veuillez re-sélectionner le fichier pour la restauration sur le web',
        );
      } else {
        // Sur les plateformes natives
        final file = File(filePath);
        if (!await file.exists()) {
          throw Exception('Le fichier de sauvegarde n\'existe pas');
        }
        jsonString = await file.readAsString();
      }

      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      await restoreFromBackup(backup);
    } catch (e) {
      throw Exception('Erreur lors de la restauration: $e');
    }
  }

  /// Restaure les données directement à partir des bytes (pour le web)
  static Future<void> restoreFromBytes(Uint8List bytes) async {
    try {
      final jsonString = String.fromCharCodes(bytes);
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      await restoreFromBackup(backup);
    } catch (e) {
      throw Exception('Erreur lors de la restauration: $e');
    }
  }

  /// Restaurer depuis une sauvegarde optimisée avec barre de progression
  static Future<void> restoreFromOptimizedBackup(
    Map<String, dynamic> backup, {
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      onProgress?.call(0.0, 'Vérification du format...');

      // Vérifier si c'est une sauvegarde optimisée
      final isOptimized = backup['optimized'] == true;

      if (SegmentedBackupService.isSegmentedBackup(backup)) {
        debugPrint('📂 Restauration depuis sauvegarde segmentée');
        await SegmentedBackupService.restoreFromSegmentedBackup(
          backup,
          onProgress: onProgress,
        );
      } else if (isOptimized) {
        debugPrint('📂 Restauration depuis sauvegarde optimisée');
        await BackupOptimizationService.restoreFromOptimizedBackup(
          backup,
          onProgress: onProgress,
        );
      } else {
        debugPrint('📂 Restauration depuis sauvegarde standard');
        await restoreFromBackup(backup);
      }
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration optimisée: $e');
      // Fallback vers la restauration standard
      await restoreFromBackup(backup);
    }
  }

  /// Restaure les données à partir d'un objet de sauvegarde (méthode standard)
  static Future<void> restoreFromBackup(Map<String, dynamic> backup) async {
    try {
      // Vérifier la version avec compatibilité
      final version = backup['version'] as String?;
      if (version == null) {
        throw Exception('Version de sauvegarde manquante');
      }

      // Gérer la compatibilité des versions
      if (!_isVersionCompatible(version)) {
        debugPrint(
          'Version incompatible: $version (actuelle: $_backupVersion)',
        );
        // Essayer de migrer ou utiliser une restauration partielle
        await _restoreWithMigration(backup, version);
        return;
      }

      final data = backup['data'] as Map<String, dynamic>;

      // Forcer la réinitialisation des services avant la restauration
      await _resetAllServices();

      // Restaurer les catégories en premier (dépendance des produits)
      if (data.containsKey('categories')) {
        final categoriesData = data['categories'] as List<dynamic>;
        final categories =
            categoriesData
                .map((json) => Category.fromJson(json as Map<String, dynamic>))
                .toList();

        // Effacer les catégories existantes et ajouter les nouvelles
        await _clearCategories();
        for (final category in categories) {
          await InventoryService.instance.addCategory(category);
        }
      }

      // Restaurer les produits
      if (data.containsKey('products')) {
        final productsData = data['products'] as List<dynamic>;
        final products =
            productsData
                .map((json) => Product.fromJson(json as Map<String, dynamic>))
                .toList();

        // Effacer les produits existants et ajouter les nouveaux
        await _clearProducts();
        for (final product in products) {
          await InventoryService.instance.addProduct(product);
        }
      }

      // Restaurer les factures
      if (data.containsKey('invoices')) {
        final invoicesData = data['invoices'] as List<dynamic>;
        final invoices =
            invoicesData
                .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
                .toList();

        await _clearInvoices();
        await InvoiceService.saveInvoices(
          invoices,
        ); // Keep using static method as it's still static
      }

      // Restaurer les tâches
      if (data.containsKey('tasks')) {
        final tasksData = data['tasks'] as List<dynamic>;
        final tasks =
            tasksData
                .map((json) => Task.fromJson(json as Map<String, dynamic>))
                .toList();

        await _clearTasks();
        for (final task in tasks) {
          await TaskService.instance.addTask(task);
        }
      }

      // Restaurer les colis
      if (data.containsKey('colis')) {
        final colisData = data['colis'] as List<dynamic>;
        final colisList =
            colisData
                .map((json) => Colis.fromJson(json as Map<String, dynamic>))
                .toList();

        await ColisService.instance.saveColis(colisList);
      }

      // Restaurer les images
      if (backup.containsKey('images')) {
        final images = backup['images'] as Map<String, dynamic>;
        await _restoreImages(images);
      }

      // Forcer la réinitialisation des services après la restauration
      await _resetAllServices();

      // Notifier tous les widgets que les données ont changé
      DataChangeNotifier.instance.notifyAllDataChanged();

      debugPrint('✅ Restauration terminée avec succès');
    } catch (e) {
      throw Exception('Erreur lors de la restauration des données: $e');
    }
  }

  /// Efface toutes les catégories
  static Future<void> _clearCategories() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('inventory_categories');
  }

  /// Efface tous les produits
  static Future<void> _clearProducts() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('inventory_products');
  }

  /// Efface toutes les factures
  static Future<void> _clearInvoices() async {
    await InvoiceService.saveInvoices([]);
  }

  /// Efface toutes les tâches
  static Future<void> _clearTasks() async {
    await TaskService.instance.clearAllTasks();
  }

  /// Réinitialise tous les services pour forcer le rechargement des données
  static Future<void> _resetAllServices() async {
    try {
      // Forcer le rechargement des données en invalidant les caches
      // Cela sera fait en appelant les méthodes de rechargement des services

      // Attendre un peu pour que les opérations de sauvegarde se terminent
      await Future.delayed(const Duration(milliseconds: 500));

      debugPrint('🔄 Services prêts pour le rechargement');
    } catch (e) {
      debugPrint('⚠️ Erreur lors de la réinitialisation des services: $e');
    }
  }

  /// Obtient des informations sur une sauvegarde sans la restaurer
  static Future<Map<String, dynamic>?> getBackupInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }

      final jsonString = await file.readAsString();
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;

      final data = backup['data'] as Map<String, dynamic>;

      return {
        'version': backup['version'],
        'timestamp': backup['timestamp'],
        'productsCount': (data['products'] as List?)?.length ?? 0,
        'categoriesCount': (data['categories'] as List?)?.length ?? 0,
        'invoicesCount': (data['invoices'] as List?)?.length ?? 0,
        'tasksCount': (data['tasks'] as List?)?.length ?? 0,
      };
    } catch (e) {
      return null;
    }
  }

  /// Vérifie si un fichier est une sauvegarde valide
  static Future<bool> isValidBackup(String filePath) async {
    final info = await getBackupInfo(filePath);
    return info != null && info['version'] == _backupVersion;
  }

  /// Vérifie si une version est compatible avec la version actuelle
  static bool _isVersionCompatible(String version) {
    // Pour l'instant, on accepte seulement la version exacte
    // Dans le futur, on pourrait implémenter une logique de compatibilité
    final currentMajor = int.tryParse(_backupVersion.split('.')[0]) ?? 1;
    final versionMajor = int.tryParse(version.split('.')[0]) ?? 0;

    // Compatible si même version majeure
    return currentMajor == versionMajor;
  }

  /// Restaure avec migration pour les versions incompatibles
  static Future<void> _restoreWithMigration(
    Map<String, dynamic> backup,
    String version,
  ) async {
    debugPrint(
      'Tentative de migration de la version $version vers $_backupVersion',
    );

    try {
      // Forcer la réinitialisation des services avant la migration
      await _resetAllServices();

      final data = backup['data'] as Map<String, dynamic>;

      // Migration basique : essayer de restaurer ce qui est compatible
      if (data.containsKey('categories')) {
        try {
          final categoriesData = data['categories'] as List<dynamic>;
          final categories =
              categoriesData
                  .map(
                    (json) => Category.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();

          await _clearCategories();
          for (final category in categories) {
            await InventoryService.instance.addCategory(category);
          }
          debugPrint('Catégories migrées avec succès');
        } catch (e) {
          debugPrint('Erreur lors de la migration des catégories: $e');
        }
      }

      if (data.containsKey('products')) {
        try {
          final productsData = data['products'] as List<dynamic>;
          final products =
              productsData
                  .map((json) => Product.fromJson(json as Map<String, dynamic>))
                  .toList();

          await _clearProducts();
          for (final product in products) {
            await InventoryService.instance.addProduct(product);
          }
          debugPrint('Produits migrés avec succès');
        } catch (e) {
          debugPrint('Erreur lors de la migration des produits: $e');
        }
      }

      // Forcer la réinitialisation des services après la migration
      await _resetAllServices();

      // Notifier tous les widgets que les données ont changé
      DataChangeNotifier.instance.notifyAllDataChanged();

      // Continuer avec les autres données...
      debugPrint('Migration partielle terminée');
    } catch (e) {
      throw Exception('Erreur lors de la migration: $e');
    }
  }

  /// Sauvegarde les images des factures
  static Future<Map<String, String>> _backupInvoiceImages(
    List<Invoice> invoices,
  ) async {
    final Map<String, String> imageBackup = {};

    for (final invoice in invoices) {
      // Sauvegarder l'image du produit principal
      if (invoice.productImagePath != null &&
          invoice.productImagePath!.isNotEmpty) {
        final imageData = await _encodeImageToBase64(invoice.productImagePath!);
        if (imageData != null) {
          imageBackup['invoice_${invoice.id}_product'] = imageData;
        }
      }

      // Sauvegarder le logo si présent
      if (invoice.logoPath != null && invoice.logoPath!.isNotEmpty) {
        final imageData = await _encodeImageToBase64(invoice.logoPath!);
        if (imageData != null) {
          imageBackup['invoice_${invoice.id}_logo'] = imageData;
        }
      }
    }

    return imageBackup;
  }

  /// Sauvegarde les images des colis
  static Future<Map<String, String>> _backupColisImages(
    List<Colis> colis,
  ) async {
    final Map<String, String> imageBackup = {};

    for (final coli in colis) {
      if (coli.photoPath.isNotEmpty) {
        final imageData = await _encodeImageToBase64(coli.photoPath);
        if (imageData != null) {
          imageBackup['colis_${coli.id}'] = imageData;
        }
      }
    }

    return imageBackup;
  }

  /// Encode une image en base64
  static Future<String?> _encodeImageToBase64(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        return base64Encode(bytes);
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'encodage de l\'image $imagePath: $e');
    }
    return null;
  }

  /// Restaure les images depuis la sauvegarde
  static Future<void> _restoreImages(Map<String, dynamic> images) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final mediaDir = Directory('${appDir.path}/media');

      if (!await mediaDir.exists()) {
        await mediaDir.create(recursive: true);
      }

      // Restaurer les images des factures
      if (images.containsKey('invoices')) {
        final invoiceImages = images['invoices'] as Map<String, dynamic>;
        for (final entry in invoiceImages.entries) {
          await _decodeAndSaveImage(
            entry.key,
            entry.value as String,
            mediaDir.path,
          );
        }
      }

      // Restaurer les images des colis
      if (images.containsKey('colis')) {
        final colisImages = images['colis'] as Map<String, dynamic>;
        for (final entry in colisImages.entries) {
          await _decodeAndSaveImage(
            entry.key,
            entry.value as String,
            mediaDir.path,
          );
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la restauration des images: $e');
    }
  }

  /// Décode et sauvegarde une image depuis base64
  static Future<void> _decodeAndSaveImage(
    String imageKey,
    String base64Data,
    String mediaPath,
  ) async {
    try {
      final bytes = base64Decode(base64Data);
      final fileName = '$imageKey.jpg'; // Extension par défaut
      final filePath = path.join(mediaPath, fileName);

      final file = File(filePath);
      await file.writeAsBytes(bytes);

      debugPrint('Image restaurée: $filePath');
    } catch (e) {
      debugPrint('Erreur lors de la restauration de l\'image $imageKey: $e');
    }
  }
}
