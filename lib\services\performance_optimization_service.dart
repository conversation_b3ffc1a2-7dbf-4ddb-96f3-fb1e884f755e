import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service pour optimiser les performances de l'application
class PerformanceOptimizationService {
  static PerformanceOptimizationService? _instance;
  static PerformanceOptimizationService get instance {
    _instance ??= PerformanceOptimizationService._internal();
    return _instance!;
  }

  PerformanceOptimizationService._internal();

  // Contrôleurs pour les opérations asynchrones
  final Map<String, Timer> _debounceTimers = {};
  final Map<String, Completer<void>> _operationCompleters = {};

  /// Débouncer une opération pour éviter les appels répétés
  void debounce(String key, Duration delay, VoidCallback operation) {
    // Annuler le timer précédent s'il existe
    _debounceTimers[key]?.cancel();

    // Créer un nouveau timer
    _debounceTimers[key] = Timer(delay, () {
      operation();
      _debounceTimers.remove(key);
    });
  }

  /// Exécuter une opération avec throttling (limitation de fréquence)
  Future<T?> throttle<T>(
    String key,
    Duration minInterval,
    Future<T> Function() operation,
  ) async {
    final now = DateTime.now();
    final prefs = await SharedPreferences.getInstance();
    final lastExecutionKey = 'throttle_$key';
    final lastExecutionString = prefs.getString(lastExecutionKey);

    if (lastExecutionString != null) {
      final lastExecution = DateTime.parse(lastExecutionString);
      final timeSinceLastExecution = now.difference(lastExecution);

      if (timeSinceLastExecution < minInterval) {
        debugPrint('🚫 Opération $key throttlée (trop récente)');
        return null;
      }
    }

    // Exécuter l'opération
    await prefs.setString(lastExecutionKey, now.toIso8601String());
    return await operation();
  }

  /// Exécuter une opération en arrière-plan sans bloquer l'UI
  Future<T> executeInBackground<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    debugPrint('🔄 Exécution en arrière-plan: $operationName');

    // Ajouter un petit délai pour laisser l'UI se mettre à jour
    await Future.delayed(const Duration(milliseconds: 50));

    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      debugPrint(
        '✅ $operationName terminé en ${stopwatch.elapsedMilliseconds}ms',
      );

      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '❌ $operationName échoué après ${stopwatch.elapsedMilliseconds}ms: $e',
      );
      rethrow;
    }
  }

  /// Exécuter plusieurs opérations en parallèle avec limitation
  Future<List<T>> executeInParallel<T>(
    List<Future<T> Function()> operations, {
    int maxConcurrency = 3,
    String? operationName,
  }) async {
    debugPrint(
      '🔄 Exécution parallèle: ${operations.length} opérations '
      '(max $maxConcurrency concurrent)',
    );

    final results = <T>[];
    final semaphore = Semaphore(maxConcurrency);

    final futures = operations.map((operation) async {
      await semaphore.acquire();
      try {
        return await operation();
      } finally {
        semaphore.release();
      }
    });

    final stopwatch = Stopwatch()..start();

    try {
      results.addAll(await Future.wait(futures));
      stopwatch.stop();

      debugPrint(
        '✅ Exécution parallèle terminée en ${stopwatch.elapsedMilliseconds}ms',
      );

      return results;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '❌ Exécution parallèle échouée après ${stopwatch.elapsedMilliseconds}ms: $e',
      );
      rethrow;
    }
  }

  /// Optimiser le chargement des données avec cache
  Future<T> loadWithCache<T>(
    String cacheKey,
    Future<T> Function() loader,
    T Function(Map<String, dynamic>) deserializer,
    Map<String, dynamic> Function(T) serializer, {
    Duration cacheDuration = const Duration(minutes: 5),
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheDataKey = 'cache_data_$cacheKey';
      final cacheTimeKey = 'cache_time_$cacheKey';

      // Vérifier si le cache est valide
      final cacheTimeString = prefs.getString(cacheTimeKey);
      if (cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        if (now.difference(cacheTime) < cacheDuration) {
          // Cache valide, charger depuis le cache
          final cacheDataString = prefs.getString(cacheDataKey);
          if (cacheDataString != null) {
            debugPrint('📂 Chargement depuis le cache: $cacheKey');
            final cacheData = Map<String, dynamic>.from(
              Uri.splitQueryString(cacheDataString),
            );
            return deserializer(cacheData);
          }
        }
      }

      // Cache invalide ou inexistant, charger les données
      debugPrint('🔄 Chargement des données: $cacheKey');
      final data = await loader();

      // Sauvegarder dans le cache
      final serializedData = serializer(data);
      await prefs.setString(
        cacheDataKey,
        Uri(
          queryParameters: serializedData.map(
            (k, v) => MapEntry(k, v.toString()),
          ),
        ).query,
      );
      await prefs.setString(cacheTimeKey, DateTime.now().toIso8601String());

      return data;
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement avec cache: $e');
      // En cas d'erreur, essayer de charger directement
      return await loader();
    }
  }

  /// Nettoyer les ressources
  void dispose() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    _operationCompleters.clear();
  }

  /// Vérifier si une opération est en cours
  bool isOperationInProgress(String key) {
    return _operationCompleters.containsKey(key);
  }

  /// Marquer le début d'une opération
  void startOperation(String key) {
    _operationCompleters[key] = Completer<void>();
  }

  /// Marquer la fin d'une opération
  void completeOperation(String key) {
    final completer = _operationCompleters.remove(key);
    if (completer != null && !completer.isCompleted) {
      completer.complete();
    }
  }

  /// Attendre qu'une opération se termine
  Future<void> waitForOperation(String key) async {
    final completer = _operationCompleters[key];
    if (completer != null) {
      await completer.future;
    }
  }
}

/// Semaphore pour limiter la concurrence
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
