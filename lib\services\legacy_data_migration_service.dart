import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/category.dart' as model;
import '../models/task.dart';
import 'data_persistence_service.dart';
import 'data_change_notifier.dart';

/// Service pour migrer les données depuis l'ancien format JSON
class LegacyDataMigrationService {
  static final LegacyDataMigrationService _instance =
      LegacyDataMigrationService._internal();
  factory LegacyDataMigrationService() => _instance;
  LegacyDataMigrationService._internal();

  static LegacyDataMigrationService get instance => _instance;

  /// Migrer les données depuis un fichier JSON legacy
  Future<MigrationResult> migrateFromJsonFile(String jsonFilePath) async {
    try {
      debugPrint('🔄 Début de la migration depuis: $jsonFilePath');

      // Lire le fichier JSON
      final file = File(jsonFilePath);
      if (!await file.exists()) {
        throw Exception('Le fichier $jsonFilePath n\'existe pas');
      }

      final jsonContent = await file.readAsString();
      final data = jsonDecode(jsonContent) as Map<String, dynamic>;

      // Extraire les données du format legacy
      final legacyData = data['data'] as Map<String, dynamic>;

      final result = MigrationResult();

      // Migrer les catégories en premier (car les produits en dépendent)
      if (legacyData.containsKey('categories')) {
        result.categoriesMigrated = await _migrateCategories(
          legacyData['categories'] as List,
        );
      }

      // Migrer les produits
      if (legacyData.containsKey('products')) {
        result.productsMigrated = await _migrateProducts(
          legacyData['products'] as List,
        );
      }

      // Migrer les factures
      if (legacyData.containsKey('invoices')) {
        result.invoicesMigrated = await _migrateInvoices(
          legacyData['invoices'] as List,
        );
      }

      // Migrer les tâches
      if (legacyData.containsKey('tasks')) {
        result.tasksMigrated = await _migrateTasks(legacyData['tasks'] as List);
      }

      result.success = true;
      result.message =
          'Migration réussie: ${result.totalMigrated} éléments migrés';

      debugPrint('✅ Migration terminée: ${result.message}');
      return result;
    } catch (e) {
      debugPrint('❌ Erreur lors de la migration: $e');
      return MigrationResult()
        ..success = false
        ..message = 'Erreur lors de la migration: $e';
    }
  }

  /// Migrer les données depuis une chaîne JSON
  Future<MigrationResult> migrateFromJsonString(String jsonString) async {
    try {
      debugPrint('🔄 Début de la migration depuis une chaîne JSON');

      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // Extraire les données du format legacy
      final legacyData = data['data'] as Map<String, dynamic>;

      final result = MigrationResult();

      // Migrer les catégories en premier (car les produits en dépendent)
      if (legacyData.containsKey('categories')) {
        result.categoriesMigrated = await _migrateCategories(
          legacyData['categories'] as List,
        );
      }

      // Migrer les produits
      if (legacyData.containsKey('products')) {
        result.productsMigrated = await _migrateProducts(
          legacyData['products'] as List,
        );
      }

      // Migrer les factures
      if (legacyData.containsKey('invoices')) {
        result.invoicesMigrated = await _migrateInvoices(
          legacyData['invoices'] as List,
        );
      }

      // Migrer les tâches
      if (legacyData.containsKey('tasks')) {
        result.tasksMigrated = await _migrateTasks(legacyData['tasks'] as List);
      }

      // Forcer la sauvegarde de toutes les données migrées
      await _finalizeDataMigration();

      result.success = true;
      result.message =
          'Migration réussie: ${result.totalMigrated} éléments migrés';

      debugPrint('✅ Migration terminée: ${result.message}');
      return result;
    } catch (e) {
      debugPrint('❌ Erreur lors de la migration: $e');
      return MigrationResult()
        ..success = false
        ..message = 'Erreur lors de la migration: $e';
    }
  }

  /// Migrer les catégories
  Future<int> _migrateCategories(List categoriesData) async {
    int migrated = 0;
    final prefs = await SharedPreferences.getInstance();
    final List<model.Category> categories = [];

    for (final categoryData in categoriesData) {
      try {
        final category = model.Category(
          id: categoryData['id'] as String,
          name: categoryData['name'] as String,
          defaultPrice: categoryData['defaultPrice']?.toDouble(),
        );

        categories.add(category);
        migrated++;
        debugPrint('✅ Catégorie migrée: ${category.name}');
      } catch (e) {
        debugPrint('❌ Erreur migration catégorie: $e');
      }
    }

    // Sauvegarder toutes les catégories
    if (categories.isNotEmpty) {
      final categoriesJson = categories.map((c) => c.toJson()).toList();
      await prefs.setString('categories', jsonEncode(categoriesJson));
    }

    return migrated;
  }

  /// Migrer les produits
  Future<int> _migrateProducts(List productsData) async {
    int migrated = 0;
    final prefs = await SharedPreferences.getInstance();
    final List<Product> products = [];

    for (final productData in productsData) {
      try {
        final product = Product(
          id: productData['id'] as String,
          name: productData['name'] as String,
          price: (productData['price'] as num).toDouble(),
          quantity: productData['quantity'] as int,
          description: productData['description'] as String? ?? '',
          categoryId: productData['categoryId'] as String,
          imageUrl: productData['imageUrl'] as String?,
        );

        products.add(product);
        migrated++;
        debugPrint('✅ Produit migré: ${product.name}');
      } catch (e) {
        debugPrint('❌ Erreur migration produit: $e');
      }
    }

    // Sauvegarder tous les produits
    if (products.isNotEmpty) {
      final productsJson = products.map((p) => p.toJson()).toList();
      await prefs.setString('products', jsonEncode(productsJson));
    }

    return migrated;
  }

  /// Migrer les factures
  Future<int> _migrateInvoices(List invoicesData) async {
    int migrated = 0;
    final prefs = await SharedPreferences.getInstance();
    final List<Invoice> invoices = [];

    for (final invoiceData in invoicesData) {
      try {
        // Convertir les items
        final items = <InvoiceItem>[];
        if (invoiceData['items'] != null) {
          for (final itemData in invoiceData['items'] as List) {
            items.add(
              InvoiceItem(
                id: itemData['id'] as String,
                name: itemData['name'] as String,
                price: (itemData['price'] as num).toDouble(),
                quantity: itemData['quantity'] as int,
                isCustom: itemData['isCustom'] as bool? ?? false,
                categoryName: itemData['categoryName'] as String? ?? '',
                isFromStock: itemData['isFromStock'] as bool? ?? false,
                productId: itemData['productId'] as String?,
              ),
            );
          }
        }

        // Convertir le statut
        InvoiceStatus status;
        switch (invoiceData['status'] as String?) {
          case 'payee':
            status = InvoiceStatus.payee;
            break;
          case 'en_cours':
            status = InvoiceStatus.enAttente;
            break;
          case 'enAttente':
            status = InvoiceStatus.enAttente;
            break;
          default:
            status = InvoiceStatus.enAttente;
        }

        // Convertir le type
        InvoiceType type;
        switch (invoiceData['type'] as String?) {
          case 'proforma':
            type = InvoiceType.proforma;
            break;
          case 'normale':
          default:
            type = InvoiceType.normale;
        }

        final invoice = Invoice(
          id: invoiceData['id'] as String,
          clientName: invoiceData['clientName'] as String,
          clientNumber: invoiceData['clientNumber'] as String,
          products: invoiceData['products'] as String? ?? '',
          items: items,
          deliveryLocation: invoiceData['deliveryLocation'] as String,
          deliveryDetails: invoiceData['deliveryDetails'] as String?,
          deliveryPrice: (invoiceData['deliveryPrice'] as num).toDouble(),
          discountAmount:
              (invoiceData['discountAmount'] as num?)?.toDouble() ?? 0.0,
          advance: (invoiceData['advance'] as num).toDouble(),
          subtotal: (invoiceData['subtotal'] as num).toDouble(),
          total: (invoiceData['total'] as num).toDouble(),
          notes: invoiceData['notes'] as String?,
          logoPath: invoiceData['logoPath'] as String?,
          footerNote: invoiceData['footerNote'] as String?,
          status: status,
          type: type,
          createdAt: DateTime.parse(invoiceData['createdAt'] as String),
          validityDate:
              invoiceData['validityDate'] != null
                  ? DateTime.parse(invoiceData['validityDate'] as String)
                  : null,
          clientAddress: invoiceData['clientAddress'] as String?,
          clientEmail: invoiceData['clientEmail'] as String?,
          companyRccm: invoiceData['companyRccm'] as String?,
          companyTaxNumber: invoiceData['companyTaxNumber'] as String?,
          paymentMethods:
              invoiceData['paymentMethods'] != null
                  ? List<String>.from(invoiceData['paymentMethods'] as List)
                  : null,
          specialConditions: invoiceData['specialConditions'] as String?,
        );

        invoices.add(invoice);
        migrated++;
        debugPrint('✅ Facture migrée: ${invoice.clientName}');
      } catch (e) {
        debugPrint('❌ Erreur migration facture: $e');
      }
    }

    // Sauvegarder toutes les factures
    if (invoices.isNotEmpty) {
      final invoicesJson = invoices.map((i) => i.toJson()).toList();
      await prefs.setString('invoices', jsonEncode(invoicesJson));
    }

    return migrated;
  }

  /// Migrer les tâches
  Future<int> _migrateTasks(List tasksData) async {
    int migrated = 0;
    final prefs = await SharedPreferences.getInstance();
    final List<Task> tasks = [];

    for (final taskData in tasksData) {
      try {
        // Convertir la priorité
        TaskPriority priority;
        switch (taskData['priority'] as String?) {
          case 'high':
            priority = TaskPriority.high;
            break;
          case 'low':
            priority = TaskPriority.low;
            break;
          case 'medium':
          default:
            priority = TaskPriority.medium;
        }

        final task = Task(
          id: taskData['id'] as String,
          title: taskData['title'] as String,
          description: taskData['description'] as String? ?? '',
          dueDate: DateTime.parse(taskData['dueDate'] as String),
          priority: priority,
          isCompleted: taskData['isCompleted'] as bool? ?? false,
          createdAt: DateTime.parse(taskData['createdAt'] as String),
        );

        tasks.add(task);
        migrated++;
        debugPrint('✅ Tâche migrée: ${task.title}');
      } catch (e) {
        debugPrint('❌ Erreur migration tâche: $e');
      }
    }

    // Sauvegarder toutes les tâches
    if (tasks.isNotEmpty) {
      final tasksJson = tasks.map((t) => t.toJson()).toList();
      await prefs.setString('tasks', jsonEncode(tasksJson));
    }

    return migrated;
  }

  /// Vérifier si des données legacy existent
  Future<bool> hasLegacyData() async {
    final prefs = await SharedPreferences.getInstance();

    // Vérifier les anciens formats de clés
    final oldKeys = [
      'old_invoices',
      'old_products',
      'old_categories',
      'old_tasks',
    ];

    for (final key in oldKeys) {
      if (prefs.containsKey(key)) {
        return true;
      }
    }

    return false;
  }

  /// Nettoyer les données legacy après migration
  Future<void> cleanupLegacyData() async {
    final prefs = await SharedPreferences.getInstance();

    final oldKeys = [
      'old_invoices',
      'old_products',
      'old_categories',
      'old_tasks',
    ];

    for (final key in oldKeys) {
      await prefs.remove(key);
    }

    debugPrint('🧹 Données legacy nettoyées');
  }

  /// Finaliser la migration des données
  Future<void> _finalizeDataMigration() async {
    try {
      debugPrint('🔄 Finalisation de la migration des données...');

      // Charger toutes les données depuis SharedPreferences
      final data = await DataPersistenceService.instance.loadAllData();

      // Forcer la sauvegarde avec le service de persistance pour standardiser les clés
      await DataPersistenceService.instance.saveAllData(
        products: data['products'],
        categories: data['categories'],
        invoices: data['invoices'],
        tasks: data['tasks'],
        colis: data['colis'],
      );

      // Marquer que les données ont été migrées et sauvegardées
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('legacy_migration_completed', true);
      await prefs.setBool('data_persistence_enabled', true);
      await prefs.setString(
        'last_migration_date',
        DateTime.now().toIso8601String(),
      );

      // Notifier tous les widgets que les données ont changé
      DataChangeNotifier.instance.notifyAllDataChanged();

      debugPrint('✅ Migration finalisée avec succès');
    } catch (e) {
      debugPrint('❌ Erreur lors de la finalisation: $e');
      rethrow;
    }
  }
}

/// Résultat de la migration
class MigrationResult {
  bool success = false;
  String message = '';
  int productsMigrated = 0;
  int categoriesMigrated = 0;
  int invoicesMigrated = 0;
  int tasksMigrated = 0;

  int get totalMigrated =>
      productsMigrated + categoriesMigrated + invoicesMigrated + tasksMigrated;

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'productsMigrated': productsMigrated,
      'categoriesMigrated': categoriesMigrated,
      'invoicesMigrated': invoicesMigrated,
      'tasksMigrated': tasksMigrated,
      'totalMigrated': totalMigrated,
    };
  }
}
