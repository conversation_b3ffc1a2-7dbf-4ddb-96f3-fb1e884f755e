import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'pages/splash_screen.dart';
import 'services/notification_service.dart';
import 'services/sync_service.dart';
import 'services/platform_uniformity_service.dart';
import 'services/version_service.dart';
import 'services/offline_config_service.dart';
import 'services/data_migration_service.dart';
import 'services/backup_service.dart';
import 'scripts/init_admin.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting('fr_FR', null);

  // Lancer l'application immédiatement
  runApp(const GeneralHCPCRMApp());

  // Effectuer toutes les initialisations en arrière-plan
  _initializeEverythingInBackground();
}

/// Initialiser tout en arrière-plan sans bloquer l'application
void _initializeEverythingInBackground() {
  // Exécuter dans un Future.microtask pour ne pas bloquer l'interface
  Future.microtask(() async {
    try {
      debugPrint('🔄 Début initialisation arrière-plan optimisée');
      
      // Délai pour éviter les conflits avec le splash screen
      await Future.delayed(const Duration(milliseconds: 200));

      // 1. Initialiser la configuration offline
      await OfflineConfigService.instance.initializeDefaultConfig();
      debugPrint('✅ Configuration offline initialisée');

      // 2. Migration des données
      await _performOptimizedInitialization();

      // 3. Initialisation des services
      await _initializeServices();

      debugPrint('✅ Initialisation complète terminée');
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation complète: $e');
      
      // Tentative de migration de fallback
      try {
        await VersionService.instance.initialize();
        final currentVersion = VersionService.instance.version;
        await DataMigrationService.instance.migrateToCurrentVersion(
          currentVersion,
        );
      } catch (fallbackError) {
        debugPrint('❌ Erreur migration fallback: $fallbackError');
      }
    }
  });
}

/// Effectuer l'initialisation optimisée de l'application
Future<void> _performOptimizedInitialization() async {
  try {
    debugPrint('🚀 Début de l\'initialisation optimisée...');

    // Obtenir la version actuelle et effectuer la migration
    await VersionService.instance.initialize();
    final currentVersion = VersionService.instance.version;
    await DataMigrationService.instance.migrateToCurrentVersion(currentVersion);

    debugPrint('✅ Initialisation optimisée terminée');
  } catch (e) {
    debugPrint('❌ Erreur lors de l\'initialisation: $e');

    // En cas d'erreur, essayer la migration traditionnelle
    try {
      await VersionService.instance.initialize();
      final currentVersion = VersionService.instance.version;
      await DataMigrationService.instance.migrateToCurrentVersion(
        currentVersion,
      );
    } catch (fallbackError) {
      debugPrint('❌ Erreur lors du fallback: $fallbackError');
    }
  }
}

/// Initialiser les services
Future<void> _initializeServices() async {
  try {
    // Vérifier si on peut utiliser les services réseau
    final canUseNetwork =
        await OfflineConfigService.instance.canPerformNetworkOperation();
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();

    debugPrint('🔒 Mode offline: ${!canUseNetwork}');
    debugPrint('🔥 Firebase: ${canUseFirebase ? "activé" : "désactivé"}');

    // Initialiser l'administrateur par défaut (toujours nécessaire)
    try {
      await AdminInitializer.ensureAdminExists();
      debugPrint('Vérification de l\'administrateur terminée');
    } catch (adminError) {
      debugPrint('⚠️ Admin initialization failed: $adminError');
    }

    // Initialiser Firebase seulement si autorisé
    if (canUseFirebase) {
      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        debugPrint('Firebase initialisé avec succès');
      } catch (firebaseError) {
        debugPrint('⚠️ Firebase initialization failed: $firebaseError');
      }
    } else {
      debugPrint('Firebase désactivé - mode offline');
    }

    // Initialiser le service de synchronisation seulement si autorisé
    final canAutoSync =
        await OfflineConfigService.instance.canPerformAutoSync();
    if (canAutoSync) {
      try {
        SyncService.instance.initializeInBackground();
        debugPrint('Service de synchronisation initialisé');
      } catch (syncError) {
        debugPrint('⚠️ Background sync initialization failed: $syncError');
      }
    } else {
      debugPrint('Synchronisation automatique désactivée - mode offline');
    }

    // Initialiser le service de notifications (local uniquement)
    try {
      await NotificationService().initialize();
      debugPrint('Service de notifications initialisé');
    } catch (notifError) {
      debugPrint('⚠️ Notification service initialization failed: $notifError');
    }

    // Démarrer les notifications périodiques seulement si autorisé
    final isOfflineMode =
        await OfflineConfigService.instance.isOfflineModeEnabled();
    if (!isOfflineMode) {
      try {
        await NotificationService().startPeriodicNotifications();
        debugPrint('Notifications périodiques démarrées');
      } catch (periodicNotifError) {
        debugPrint('⚠️ Periodic notifications failed: $periodicNotifError');
      }
    } else {
      debugPrint('Notifications périodiques désactivées - mode offline');
    }

    // Initialiser le service de version (local uniquement)
    try {
      await VersionService.instance.initialize();
      debugPrint('Service de version initialisé');
    } catch (versionError) {
      debugPrint('⚠️ Version service initialization failed: $versionError');
    }

    // Effectuer une sauvegarde automatique locale en arrière-plan
    _performAutomaticBackup();
  } catch (e) {
    debugPrint('Erreur lors de l\'initialisation des services: $e');
  }
}

/// Effectuer une sauvegarde automatique locale en arrière-plan
void _performAutomaticBackup() {
  Future.microtask(() async {
    try {
      debugPrint('🔄 Démarrage de la sauvegarde automatique...');

      // Créer une sauvegarde locale incluant les images
      final backupPath = await BackupService.exportBackupToFile();

      debugPrint('✅ Sauvegarde automatique terminée: $backupPath');
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde automatique: $e');
    }
  });
}

class GeneralHCPCRMApp extends StatelessWidget {
  const GeneralHCPCRMApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'General HCP CRM',
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('fr', 'FR'), Locale('en', 'US')],
      locale: const Locale('fr', 'FR'),
      theme: PlatformUniformityService.getUniformTheme(),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
