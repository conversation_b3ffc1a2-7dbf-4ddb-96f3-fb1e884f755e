import 'package:flutter/material.dart';
import 'lib/services/gamification_service.dart';
import 'lib/services/invoice_service.dart';
import 'lib/models/invoice.dart';
import 'lib/models/invoice_item.dart';

/// Test simple pour vérifier que les corrections fonctionnent
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Test des corrections...');
  
  // Test 1: Vérifier que processNewInvoice ne lance plus d'erreur null
  try {
    final gamificationService = GamificationService.instance;
    
    // Créer une facture de test
    final testInvoice = Invoice(
      id: 'test-123',
      clientName: 'Client Test',
      clientNumber: '123456789',
      items: [
        InvoiceItem(
          id: 'item-1',
          name: 'Produit Test',
          quantity: 2,
          unitPrice: 5000,
          total: 10000,
          isFromStock: false,
        ),
      ],
      deliveryLocation: 'Test Location',
      deliveryPrice: 1000,
      status: InvoiceStatus.payee,
      createdAt: DateTime.now(),
    );
    
    final reward = await gamificationService.processNewInvoice(testInvoice);
    print('✅ Test 1 réussi: processNewInvoice fonctionne');
    print('   Points gagnés: ${reward.pointsEarned}');
    print('   Messages: ${reward.messages.join(', ')}');
    
  } catch (e) {
    print('❌ Test 1 échoué: $e');
  }
  
  // Test 2: Vérifier que updateInvoiceWithGamification fonctionne
  try {
    final invoiceService = InvoiceService();
    
    final originalInvoice = Invoice(
      id: 'test-456',
      clientName: 'Client Test 2',
      clientNumber: '987654321',
      items: [
        InvoiceItem(
          id: 'item-2',
          name: 'Produit Test 2',
          quantity: 1,
          unitPrice: 3000,
          total: 3000,
          isFromStock: false,
        ),
      ],
      deliveryLocation: 'Test Location 2',
      deliveryPrice: 500,
      status: InvoiceStatus.enAttente,
      createdAt: DateTime.now(),
    );
    
    final updatedInvoice = originalInvoice.copyWith(status: InvoiceStatus.payee);
    
    final reward = await invoiceService.updateInvoiceWithGamification(
      originalInvoice,
      updatedInvoice,
    );
    
    print('✅ Test 2 réussi: updateInvoiceWithGamification fonctionne');
    if (reward != null) {
      print('   Récompense reçue: ${reward.pointsEarned} points');
    } else {
      print('   Aucune récompense (normal pour ce test)');
    }
    
  } catch (e) {
    print('❌ Test 2 échoué: $e');
  }
  
  print('🎉 Tests terminés!');
}
