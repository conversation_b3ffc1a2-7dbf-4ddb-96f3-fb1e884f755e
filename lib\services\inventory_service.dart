import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart' as model;
import 'package:uuid/uuid.dart';
import 'firebase_service.dart';

class InventoryService {
  static InventoryService? _instance;
  static InventoryService get instance {
    _instance ??= InventoryService._internal();
    return _instance!;
  }

  InventoryService._internal();

  static const String _productsKey = 'inventory_products';
  static const String _categoriesKey = 'inventory_categories';
  final List<Product> _products = [];
  final List<model.Category> _categories = [];
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;

  Future<void> _initializeIfNeeded() async {
    if (!_isInitialized) {
      await _loadFromStorage();
      _isInitialized = true;
    }
  }

  Future<void> _loadFromStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Charger les produits
    final productsJson = prefs.getString(_productsKey);
    if (productsJson != null) {
      final List<dynamic> productsList = jsonDecode(productsJson);
      _products.clear();
      _products.addAll(
        productsList.map((json) => Product.fromJson(json)).toList(),
      );
    }

    // Charger les catégories
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson != null) {
      final List<dynamic> categoriesList = jsonDecode(categoriesJson);
      _categories.clear();
      _categories.addAll(
        categoriesList.map((json) => model.Category.fromJson(json)).toList(),
      );
    }
  }

  Future<void> _saveToStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Sauvegarder les produits
    final productsJson = jsonEncode(_products.map((p) => p.toJson()).toList());
    await prefs.setString(_productsKey, productsJson);

    // Sauvegarder les catégories
    final categoriesJson = jsonEncode(
      _categories.map((c) => c.toJson()).toList(),
    );
    await prefs.setString(_categoriesKey, categoriesJson);
  }

  // Product methods - Version simplifiée et rapide
  Future<List<Product>> getProducts() async {
    try {
      debugPrint('🔄 Chargement des produits...');

      // Charger directement depuis le cache local
      await _initializeIfNeeded();

      debugPrint('✅ ${_products.length} produits chargés depuis le cache');
      return List.unmodifiable(_products);
    } catch (e) {
      debugPrint('❌ Erreur chargement produits: $e');
      return [];
    }
  }

  Future<Product> addProduct(Product product) async {
    await _initializeIfNeeded();
    final newProduct = product.copyWith(id: _uuid.v4());

    try {
      // Essayer de sauvegarder dans Firebase d'abord
      await FirebaseService.instance.addProduct(newProduct);
    } catch (e) {
      debugPrint('Firebase save failed, saving locally: $e');
    }

    // Toujours sauvegarder localement
    _products.add(newProduct);
    await _saveToStorage();
    return newProduct;
  }

  Future<Product> updateProduct(Product product) async {
    await _initializeIfNeeded();
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      try {
        // Essayer de mettre à jour dans Firebase d'abord
        await FirebaseService.instance.updateProduct(product);
      } catch (e) {
        debugPrint('Firebase update failed, updating locally: $e');
      }

      // Toujours mettre à jour localement
      _products[index] = product;
      await _saveToStorage();
      return product;
    } else {
      throw Exception('Product not found');
    }
  }

  Future<void> deleteProduct(String productId) async {
    await _initializeIfNeeded();

    try {
      // Essayer de supprimer de Firebase d'abord
      await FirebaseService.instance.deleteProduct(productId);
    } catch (e) {
      debugPrint('Firebase delete failed, deleting locally: $e');
    }

    // Toujours supprimer localement
    _products.removeWhere((p) => p.id == productId);
    await _saveToStorage();
  }

  // Category methods - Version simplifiée et rapide
  Future<List<model.Category>> getCategories() async {
    try {
      debugPrint('🔄 Chargement des catégories...');

      // Charger directement depuis le cache local
      await _initializeIfNeeded();

      if (_categories.isNotEmpty) {
        debugPrint(
          '✅ ${_categories.length} catégories chargées depuis le cache',
        );
        return List.unmodifiable(_categories);
      }

      // Si aucune catégorie n'existe, créer des catégories par défaut
      debugPrint('📝 Création des catégories par défaut...');
      final defaultCategories = [
        model.Category(id: _uuid.v4(), name: 'Électronique'),
        model.Category(id: _uuid.v4(), name: 'Vêtements'),
        model.Category(id: _uuid.v4(), name: 'Alimentation'),
        model.Category(id: _uuid.v4(), name: 'Maison & Jardin'),
        model.Category(id: _uuid.v4(), name: 'Santé & Beauté'),
      ];

      // Sauvegarder localement
      _categories.addAll(defaultCategories);
      await _saveToStorage();

      debugPrint('✅ ${_categories.length} catégories par défaut créées');
      return List.unmodifiable(_categories);
    } catch (e) {
      debugPrint('❌ Erreur chargement catégories: $e');

      // En cas d'erreur, au moins créer des catégories par défaut
      try {
        if (_categories.isEmpty) {
          _categories.addAll([
            model.Category(id: _uuid.v4(), name: 'Électronique'),
            model.Category(id: _uuid.v4(), name: 'Vêtements'),
            model.Category(id: _uuid.v4(), name: 'Alimentation'),
          ]);
          await _saveToStorage();
        }
        return List.unmodifiable(_categories);
      } catch (e2) {
        debugPrint('❌ Erreur création catégories par défaut: $e2');
        return [];
      }
    }
  }

  Future<model.Category> addCategory(model.Category category) async {
    await _initializeIfNeeded();
    final newCategory = category.copyWith(id: _uuid.v4());

    try {
      // Essayer de sauvegarder dans Firebase d'abord
      await FirebaseService.instance.addCategory(newCategory);
    } catch (e) {
      debugPrint('Firebase save failed, saving locally: $e');
    }

    // Toujours sauvegarder localement
    _categories.add(newCategory);
    await _saveToStorage();
    return newCategory;
  }

  Future<model.Category> updateCategory(model.Category category) async {
    await _initializeIfNeeded();
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      try {
        // Essayer de mettre à jour dans Firebase d'abord
        await FirebaseService.instance.updateCategory(category);
      } catch (e) {
        debugPrint('Firebase update failed, updating locally: $e');
      }

      // Toujours mettre à jour localement
      _categories[index] = category;
      await _saveToStorage();
      return category;
    } else {
      throw Exception('Category not found');
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    await _initializeIfNeeded();
    // Vérifier si la catégorie est utilisée par des produits avant de la supprimer
    final isCategoryUsed = _products.any(
      (product) => product.categoryId == categoryId,
    );
    if (isCategoryUsed) {
      throw Exception('Category is in use and cannot be deleted.');
    }

    try {
      // Essayer de supprimer de Firebase d'abord
      await FirebaseService.instance.deleteCategory(categoryId);
    } catch (e) {
      debugPrint('Firebase delete failed, deleting locally: $e');
    }

    // Toujours supprimer localement
    _categories.removeWhere((c) => c.id == categoryId);
    await _saveToStorage();
  }

  // Helper pour obtenir une catégorie par ID
  Future<model.Category?> getCategoryById(String categoryId) async {
    await _initializeIfNeeded();
    try {
      return _categories.firstWhere((c) => c.id == categoryId);
    } catch (e) {
      return null;
    }
  }
}
