import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart';
import 'package:uuid/uuid.dart';
import 'firebase_service.dart';
import 'data_persistence_service.dart';
import 'performance_optimization_service.dart';

class InventoryService {
  static InventoryService? _instance;
  static InventoryService get instance {
    _instance ??= InventoryService._internal();
    return _instance!;
  }

  InventoryService._internal();

  static const String _productsKey = 'inventory_products';
  static const String _categoriesKey = 'inventory_categories';
  final List<Product> _products = [];
  final List<Category> _categories = [];
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;

  Future<void> _initializeIfNeeded() async {
    if (!_isInitialized) {
      await _loadFromStorage();
      _isInitialized = true;
    }
  }

  Future<void> _loadFromStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Charger les produits
    final productsJson = prefs.getString(_productsKey);
    if (productsJson != null) {
      final List<dynamic> productsList = jsonDecode(productsJson);
      _products.clear();
      _products.addAll(
        productsList.map((json) => Product.fromJson(json)).toList(),
      );
    }

    // Charger les catégories
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson != null) {
      final List<dynamic> categoriesList = jsonDecode(categoriesJson);
      _categories.clear();
      _categories.addAll(
        categoriesList.map((json) => Category.fromJson(json)).toList(),
      );
    }
  }

  Future<void> _saveToStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Sauvegarder les produits
    final productsJson = jsonEncode(_products.map((p) => p.toJson()).toList());
    await prefs.setString(_productsKey, productsJson);

    // Sauvegarder les catégories
    final categoriesJson = jsonEncode(
      _categories.map((c) => c.toJson()).toList(),
    );
    await prefs.setString(_categoriesKey, categoriesJson);
  }

  // Product methods
  Future<List<Product>> getProducts() async {
    try {
      // D'abord essayer de charger depuis le service hybride
      final hybridProducts = await FirebaseService.instance.getAllProducts();

      // Si le service hybride retourne des données, les utiliser
      if (hybridProducts.isNotEmpty) {
        return hybridProducts;
      }

      // Sinon, charger depuis SharedPreferences (données locales existantes)
      await _initializeIfNeeded();

      if (_products.isNotEmpty) {
        // Migrer les données locales vers Supabase en arrière-plan
        _migrateLocalProductsToSupabase(_products);
        return List.unmodifiable(_products);
      }

      return [];
    } catch (e) {
      // Fallback vers les données locales en cas d'erreur
      await _initializeIfNeeded();
      return List.unmodifiable(_products);
    }
  }

  // Migrer les produits locaux vers Supabase en arrière-plan
  void _migrateLocalProductsToSupabase(List<Product> products) {
    Future.microtask(() async {
      try {
        for (final product in products) {
          await FirebaseService.instance.addProduct(product);
        }
      } catch (e) {
        // Ignorer les erreurs de migration
      }
    });
  }

  Future<Product> addProduct(Product product) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    final newProduct = product.copyWith(id: _uuid.v4());
    _products.add(newProduct);
    await _saveToStorage();
    return newProduct;
  }

  Future<Product> updateProduct(Product product) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index] = product;
      await _saveToStorage();
      return product;
    } else {
      throw Exception('Product not found');
    }
  }

  Future<void> deleteProduct(String productId) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    _products.removeWhere((p) => p.id == productId);
    await _saveToStorage();
  }

  // Category methods
  Future<List<Category>> getCategories() async {
    try {
      // D'abord essayer de charger depuis le service hybride
      final hybridCategories =
          await FirebaseService.instance.getAllCategories();

      // Si le service hybride retourne des données, les utiliser
      if (hybridCategories.isNotEmpty) {
        return hybridCategories;
      }

      // Sinon, charger depuis SharedPreferences (données locales existantes)
      await _initializeIfNeeded();

      if (_categories.isNotEmpty) {
        // Migrer les données locales vers Supabase en arrière-plan
        _migrateLocalCategoriesToSupabase(_categories);
        return List.unmodifiable(_categories);
      }

      // Si aucune donnée n'existe, créer les catégories par défaut
      final defaultCategories = [
        Category(id: _uuid.v4(), name: 'Électronique'),
        Category(id: _uuid.v4(), name: 'Vêtements'),
        Category(id: _uuid.v4(), name: 'Alimentation'),
      ];

      // Sauvegarder localement et dans Supabase
      _categories.addAll(defaultCategories);
      await _saveToStorage();

      for (final category in defaultCategories) {
        await FirebaseService.instance.addCategory(category);
      }

      return defaultCategories;
    } catch (e) {
      // Fallback vers les données locales en cas d'erreur
      await _initializeIfNeeded();
      if (_categories.isEmpty) {
        _categories.addAll([
          Category(id: _uuid.v4(), name: 'Électronique'),
          Category(id: _uuid.v4(), name: 'Vêtements'),
          Category(id: _uuid.v4(), name: 'Alimentation'),
        ]);
        await _saveToStorage();
      }
      return List.unmodifiable(_categories);
    }
  }

  // Migrer les catégories locales vers Supabase en arrière-plan
  void _migrateLocalCategoriesToSupabase(List<Category> categories) {
    Future.microtask(() async {
      try {
        for (final category in categories) {
          await FirebaseService.instance.addCategory(category);
        }
      } catch (e) {
        // Ignorer les erreurs de migration
      }
    });
  }

  Future<Category> addCategory(Category category) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    final newCategory = category.copyWith(id: _uuid.v4());
    _categories.add(newCategory);
    await _saveToStorage();
    return newCategory;
  }

  Future<Category> updateCategory(Category category) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      _categories[index] = category;
      await _saveToStorage();
      return category;
    } else {
      throw Exception('Category not found');
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 300));
    // Vérifier si la catégorie est utilisée par des produits avant de la supprimer
    final isCategoryUsed = _products.any(
      (product) => product.categoryId == categoryId,
    );
    if (isCategoryUsed) {
      throw Exception('Category is in use and cannot be deleted.');
    }
    _categories.removeWhere((c) => c.id == categoryId);
    await _saveToStorage();
  }

  // Helper pour obtenir une catégorie par ID
  Future<Category?> getCategoryById(String categoryId) async {
    await _initializeIfNeeded();
    await Future.delayed(const Duration(milliseconds: 100));
    try {
      return _categories.firstWhere((c) => c.id == categoryId);
    } catch (e) {
      return null;
    }
  }
}
