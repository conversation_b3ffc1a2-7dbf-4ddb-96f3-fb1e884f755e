import 'package:flutter/material.dart';
import 'main_navigation_page.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _textController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    super.initState();

    // Configuration des contrôleurs d'animation
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Configuration des animations
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _textFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOutBack),
    );

    _navigateQuickly();
  }

  void _navigateQuickly() async {
    // Démarrer les animations rapidement
    _fadeController.forward();
    _scaleController.forward();
    _textController.forward();

    // Attendre suffisamment pour éviter les conflits avec l'initialisation
    await Future.delayed(const Duration(milliseconds: 500));
    _navigateToDashboard();
  }

  /// Effectuer les initialisations en arrière-plan après la navigation
  void _performBackgroundInitialization() {
    // Exécuter dans un Future.microtask pour ne pas bloquer l'interface
    Future.microtask(() async {
      try {
        debugPrint('🔄 Initialisation en arrière-plan...');

        // Les initialisations sont déjà faites dans main.dart
        // Ici on peut juste logger que l'application est prête
        debugPrint('✅ Application prête et fonctionnelle');
      } catch (e) {
        debugPrint('❌ Erreur lors de l\'initialisation en arrière-plan: $e');
      }
    });
  }

  void _navigateToDashboard() {
    if (!mounted) return;

    debugPrint('🚀 Navigation vers le dashboard...');

    // Navigation directe et simple vers le dashboard
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MainNavigationPage()),
    );

    // Effectuer les initialisations en arrière-plan après la navigation
    _performBackgroundInitialization();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo animé
            AnimatedBuilder(
              animation: _scaleController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: SizedBox(
                      width: 200,
                      height: 200,
                      child: Image.asset(
                        'assets/images/logo_entreprise.png',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback si l'image n'est pas trouvée
                          return Container(
                            color: Colors.white,
                            child: const Icon(
                              Icons.business,
                              size: 100,
                              color: Colors.black,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 40),

            // Texte animé
            SlideTransition(
              position: _textSlideAnimation,
              child: FadeTransition(
                opacity: _textFadeAnimation,
                child: Column(
                  children: [
                    Text(
                      'HCP-DESIGN',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[900],
                        letterSpacing: 2,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Générateur de Factures',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 30),

                    // Indicateur de chargement
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.blue[700]!,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
