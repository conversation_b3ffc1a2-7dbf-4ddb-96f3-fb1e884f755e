import 'package:flutter/foundation.dart';
import 'dart:io';

/// Service d'optimisation pour les appareils mobiles - VERSION SIMPLIFIÉE ET FIABLE
/// TOUTES LES OPTIMISATIONS SONT DÉSACTIVÉES POUR ÉVITER LES PROBLÈMES DE PERFORMANCE
class MobileOptimizationService {
  static MobileOptimizationService? _instance;
  static MobileOptimizationService get instance =>
      _instance ??= MobileOptimizationService._internal();

  MobileOptimizationService._internal();

  bool? _isMobile;

  /// Vérifie si l'application fonctionne sur un appareil mobile
  bool get isMobile {
    if (_isMobile != null) return _isMobile!;

    try {
      if (kIsWeb) {
        _isMobile = false;
      } else {
        try {
          _isMobile = Platform.isAndroid || Platform.isIOS;
        } catch (platformError) {
          debugPrint('Platform detection error: $platformError');
          _isMobile = !kIsWeb;
        }
      }
    } catch (e) {
      debugPrint('Mobile detection error: $e');
      _isMobile = !kIsWeb;
    }

    return _isMobile!;
  }

  /// DÉSACTIVÉ: Toujours retourner false pour éviter les optimisations problématiques
  Future<bool> get isLowEndDevice async {
    debugPrint('🚀 Optimisations mobiles DÉSACTIVÉES pour de meilleures performances');
    return false; // Toujours false = pas d'optimisations
  }

  /// Mode optimisé désactivé pour de meilleures performances
  Future<bool> shouldUseOptimizedMode() async {
    debugPrint('📱 Mode optimisé DÉSACTIVÉ - utilisation du mode standard');
    return false; // Toujours false = mode standard
  }

  /// Taille de lot standard (pas d'optimisation)
  Future<int> getOptimalBatchSize() async {
    return 100; // Taille standard pour tous les appareils
  }

  /// Délai standard (pas d'optimisation)
  Future<Duration> getOptimalDelay() async {
    return Duration.zero; // Pas de délai artificiel
  }

  /// Pas de nettoyage forcé (éviter les problèmes)
  void forceGarbageCollection() {
    debugPrint('🧹 Nettoyage mémoire DÉSACTIVÉ pour éviter les problèmes');
    // Ne rien faire - éviter les problèmes
  }

  /// Information d'optimisation
  Future<String> getOptimizationInfo() async {
    if (isMobile) {
      return 'Mobile détecté - Mode standard activé (optimisations désactivées)';
    } else {
      return 'Desktop - Mode standard';
    }
  }

  /// Optimisation désactivée
  Future<void> optimizeForMobile() async {
    debugPrint('📱 Optimisations mobiles DÉSACTIVÉES');
    // Ne rien faire
  }

  /// Paramètres de sauvegarde standard (pas d'optimisation)
  Future<Map<String, dynamic>> getBackupOptimizationParams() async {
    return {
      'batchSize': 100,
      'delay': 0,
      'useCompression': false,
      'useBackgroundSave': false,
      'maxMemoryUsage': 200,
      'enableDebugLogs': kDebugMode,
    };
  }

  /// Réinitialiser
  void reset() {
    _isMobile = null;
    debugPrint('🔄 MobileOptimizationService réinitialisé');
  }
}
