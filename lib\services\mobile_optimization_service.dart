import 'package:flutter/foundation.dart';
import 'dart:io';

/// Service d'optimisation pour les appareils mobiles
class MobileOptimizationService {
  static MobileOptimizationService? _instance;
  static MobileOptimizationService get instance =>
      _instance ??= MobileOptimizationService._internal();

  MobileOptimizationService._internal();

  bool? _isMobile;
  bool? _isLowEndDevice;

  /// Vérifie si l'application fonctionne sur un appareil mobile
  bool get isMobile {
    if (_isMobile != null) return _isMobile!;

    try {
      if (kIsWeb) {
        _isMobile = false;
      } else {
        _isMobile = Platform.isAndroid || Platform.isIOS;
      }
    } catch (e) {
      // Fallback si Platform n'est pas disponible
      _isMobile = !kIsWeb;
    }

    return _isMobile!;
  }

  /// Vérifie si c'est un appareil mobile de faible performance
  Future<bool> get isLowEndDevice async {
    if (_isLowEndDevice != null) return _isLowEndDevice!;

    if (!isMobile) {
      _isLowEndDevice = false;
      return false;
    }

    try {
      // Utiliser les informations système pour détecter les appareils faibles
      final deviceInfo = await _getDeviceInfo();

      // Critères pour détecter un appareil faible :
      // - RAM < 3GB
      // - Processeur ancien
      // - Android < 8.0 ou iOS < 12.0
      _isLowEndDevice = deviceInfo['isLowEnd'] ?? false;
    } catch (e) {
      debugPrint('Erreur détection appareil faible: $e');
      // Par défaut, considérer comme appareil faible sur mobile pour être sûr
      _isLowEndDevice = true;
    }

    return _isLowEndDevice!;
  }

  /// Obtenir les informations de l'appareil
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidDeviceInfo();
      } else if (Platform.isIOS) {
        return await _getIOSDeviceInfo();
      }
    } catch (e) {
      debugPrint('Erreur récupération info appareil: $e');
    }

    return {'isLowEnd': true}; // Par défaut, considérer comme faible
  }

  /// Informations spécifiques Android
  Future<Map<String, dynamic>> _getAndroidDeviceInfo() async {
    try {
      // Simuler la détection d'appareil Android faible
      // Dans une vraie implémentation, utiliser device_info_plus
      return {
        'isLowEnd': true, // Par sécurité, considérer comme faible
        'platform': 'android',
      };
    } catch (e) {
      return {'isLowEnd': true};
    }
  }

  /// Informations spécifiques iOS
  Future<Map<String, dynamic>> _getIOSDeviceInfo() async {
    try {
      // Simuler la détection d'appareil iOS faible
      // Dans une vraie implémentation, utiliser device_info_plus
      return {
        'isLowEnd': false, // iOS généralement plus performant
        'platform': 'ios',
      };
    } catch (e) {
      return {'isLowEnd': true};
    }
  }

  /// Obtenir la taille de lot optimale pour les opérations
  Future<int> getOptimalBatchSize() async {
    if (!isMobile) return 100; // Desktop peut gérer plus

    final isLowEnd = await isLowEndDevice;
    if (isLowEnd) {
      return 10; // Très petits lots pour appareils faibles
    } else {
      return 25; // Lots moyens pour appareils mobiles normaux
    }
  }

  /// Obtenir le délai optimal entre les opérations
  Future<Duration> getOptimalDelay() async {
    if (!isMobile) return Duration.zero; // Pas de délai sur desktop

    final isLowEnd = await isLowEndDevice;
    if (isLowEnd) {
      return const Duration(
        milliseconds: 50,
      ); // Plus de délai pour appareils faibles
    } else {
      return const Duration(
        milliseconds: 10,
      ); // Délai minimal pour appareils normaux
    }
  }

  /// Vérifier si on doit utiliser le mode optimisé
  Future<bool> shouldUseOptimizedMode() async {
    return isMobile; // Toujours optimiser sur mobile
  }

  /// Obtenir les paramètres d'optimisation pour les sauvegardes
  Future<Map<String, dynamic>> getBackupOptimizationParams() async {
    final isLowEnd = await isLowEndDevice;

    return {
      'batchSize': await getOptimalBatchSize(),
      'delay': await getOptimalDelay(),
      'useCompression': isMobile, // Compression sur mobile
      'useBackgroundSave': isMobile, // Sauvegarde en arrière-plan sur mobile
      'maxMemoryUsage': isLowEnd ? 50 : 100, // MB
      'enableDebugLogs': kDebugMode && isMobile,
    };
  }

  /// Forcer le garbage collection sur mobile
  void forceGarbageCollection() {
    if (isMobile && kDebugMode) {
      try {
        // Forcer le nettoyage mémoire sur mobile
        debugPrint('🧹 Nettoyage mémoire forcé (mobile)');
      } catch (e) {
        debugPrint('Erreur nettoyage mémoire: $e');
      }
    }
  }

  /// Optimiser les performances pour mobile
  Future<void> optimizeForMobile() async {
    if (!isMobile) return;

    try {
      debugPrint('📱 Optimisation mobile activée');

      // Réduire la fréquence des animations si appareil faible
      final isLowEnd = await isLowEndDevice;
      if (isLowEnd) {
        debugPrint('⚡ Mode performance réduite activé');
      }

      // Autres optimisations...
    } catch (e) {
      debugPrint('Erreur optimisation mobile: $e');
    }
  }

  /// Obtenir un message d'information sur l'optimisation
  Future<String> getOptimizationInfo() async {
    if (!isMobile) {
      return 'Mode Desktop - Performance maximale';
    }

    final isLowEnd = await isLowEndDevice;
    final batchSize = await getOptimalBatchSize();

    if (isLowEnd) {
      return 'Mobile faible performance - Lots de $batchSize éléments';
    } else {
      return 'Mobile performance normale - Lots de $batchSize éléments';
    }
  }

  /// Réinitialiser la détection (pour les tests)
  void reset() {
    _isMobile = null;
    _isLowEndDevice = null;
  }
}
