import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service pour gérer les permissions d'alarmes et de rappels Android
class AlarmPermissionService {
  static final AlarmPermissionService _instance = AlarmPermissionService._internal();
  factory AlarmPermissionService() => _instance;
  AlarmPermissionService._internal();

  static const MethodChannel _channel = MethodChannel('alarm_permissions');

  /// Vérifie si les permissions d'alarmes exactes sont accordées
  Future<bool> hasExactAlarmPermission() async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      // Pour Android 12+ (API 31+)
      if (await _getAndroidVersion() >= 31) {
        final result = await _channel.invokeMethod('hasExactAlarmPermission');
        return result ?? false;
      }
      return true;
    } catch (e) {
      debugPrint('Erreur vérification permission alarme exacte: $e');
      return false;
    }
  }

  /// Demande la permission pour les alarmes exactes
  Future<bool> requestExactAlarmPermission(BuildContext context) async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      // Vérifier d'abord si on a déjà la permission
      if (await hasExactAlarmPermission()) {
        return true;
      }

      // Afficher un dialog explicatif
      final shouldRequest = await _showPermissionDialog(
        context,
        'Permission Alarmes Exactes',
        'Cette application a besoin de la permission pour programmer des alarmes exactes afin de vous envoyer des rappels importants.\n\nVoulez-vous accorder cette permission ?',
        'Accorder',
        'Annuler',
      );

      if (!shouldRequest) return false;

      // Demander la permission
      final result = await _channel.invokeMethod('requestExactAlarmPermission');
      return result ?? false;
    } catch (e) {
      debugPrint('Erreur demande permission alarme exacte: $e');
      return false;
    }
  }

  /// Vérifie si l'optimisation de batterie est désactivée
  Future<bool> isBatteryOptimizationDisabled() async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      final result = await _channel.invokeMethod('isBatteryOptimizationDisabled');
      return result ?? false;
    } catch (e) {
      debugPrint('Erreur vérification optimisation batterie: $e');
      return false;
    }
  }

  /// Demande de désactiver l'optimisation de batterie
  Future<bool> requestDisableBatteryOptimization(BuildContext context) async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      // Vérifier d'abord si c'est déjà désactivé
      if (await isBatteryOptimizationDisabled()) {
        return true;
      }

      // Afficher un dialog explicatif
      final shouldRequest = await _showPermissionDialog(
        context,
        'Optimisation de Batterie',
        'Pour garantir le bon fonctionnement des rappels, veuillez désactiver l\'optimisation de batterie pour cette application.\n\nCela permettra à l\'application de fonctionner en arrière-plan.',
        'Paramètres',
        'Ignorer',
      );

      if (!shouldRequest) return false;

      // Ouvrir les paramètres d'optimisation de batterie
      final result = await _channel.invokeMethod('requestDisableBatteryOptimization');
      return result ?? false;
    } catch (e) {
      debugPrint('Erreur demande désactivation optimisation batterie: $e');
      return false;
    }
  }

  /// Vérifie toutes les permissions nécessaires pour les alarmes
  Future<bool> checkAllAlarmPermissions() async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      // Vérifier les permissions de base
      final notificationPermission = await Permission.notification.isGranted;
      final exactAlarmPermission = await hasExactAlarmPermission();
      final batteryOptimization = await isBatteryOptimizationDisabled();
      
      debugPrint('🔔 Permissions alarmes:');
      debugPrint('  - Notifications: $notificationPermission');
      debugPrint('  - Alarmes exactes: $exactAlarmPermission');
      debugPrint('  - Optimisation batterie: $batteryOptimization');
      
      return notificationPermission && exactAlarmPermission && batteryOptimization;
    } catch (e) {
      debugPrint('Erreur vérification permissions alarmes: $e');
      return false;
    }
  }

  /// Demande toutes les permissions nécessaires pour les alarmes
  Future<bool> requestAllAlarmPermissions(BuildContext context) async {
    if (!Platform.isAndroid || kIsWeb) return true;
    
    try {
      // 1. Permission notifications
      final notificationStatus = await Permission.notification.request();
      if (!notificationStatus.isGranted) {
        _showErrorSnackBar(context, 'Permission notifications requise');
        return false;
      }

      // 2. Permission alarmes exactes
      final exactAlarmPermission = await requestExactAlarmPermission(context);
      if (!exactAlarmPermission) {
        _showErrorSnackBar(context, 'Permission alarmes exactes requise');
        return false;
      }

      // 3. Désactivation optimisation batterie
      final batteryOptimization = await requestDisableBatteryOptimization(context);
      if (!batteryOptimization) {
        _showWarningSnackBar(context, 'Optimisation batterie recommandée');
      }

      return true;
    } catch (e) {
      debugPrint('Erreur demande permissions alarmes: $e');
      _showErrorSnackBar(context, 'Erreur lors de la demande de permissions');
      return false;
    }
  }

  /// Ouvre les paramètres d'alarmes et rappels
  Future<void> openAlarmSettings() async {
    if (!Platform.isAndroid || kIsWeb) return;
    
    try {
      await _channel.invokeMethod('openAlarmSettings');
    } catch (e) {
      debugPrint('Erreur ouverture paramètres alarmes: $e');
    }
  }

  /// Obtient la version Android
  Future<int> _getAndroidVersion() async {
    try {
      final version = await _channel.invokeMethod('getAndroidVersion');
      return version ?? 30;
    } catch (e) {
      debugPrint('Erreur obtention version Android: $e');
      return 30;
    }
  }

  /// Affiche un dialog de demande de permission
  Future<bool> _showPermissionDialog(
    BuildContext context,
    String title,
    String message,
    String confirmText,
    String cancelText,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.alarm, color: Colors.orange),
              const SizedBox(width: 8),
              Text(title),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Affiche un SnackBar d'erreur
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Paramètres',
          textColor: Colors.white,
          onPressed: () => openAlarmSettings(),
        ),
      ),
    );
  }

  /// Affiche un SnackBar d'avertissement
  void _showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'Paramètres',
          textColor: Colors.white,
          onPressed: () => openAlarmSettings(),
        ),
      ),
    );
  }

  /// Vérifie et demande les permissions au démarrage de l'application
  Future<void> initializeAlarmPermissions(BuildContext context) async {
    if (!Platform.isAndroid || kIsWeb) return;
    
    try {
      debugPrint('🔔 Initialisation des permissions d\'alarmes...');
      
      // Vérifier les permissions actuelles
      final hasPermissions = await checkAllAlarmPermissions();
      
      if (!hasPermissions) {
        // Attendre un peu pour que l'interface soit prête
        await Future.delayed(const Duration(seconds: 2));
        
        // Demander les permissions manquantes
        await requestAllAlarmPermissions(context);
      }
      
      debugPrint('✅ Permissions d\'alarmes initialisées');
    } catch (e) {
      debugPrint('❌ Erreur initialisation permissions alarmes: $e');
    }
  }
}
