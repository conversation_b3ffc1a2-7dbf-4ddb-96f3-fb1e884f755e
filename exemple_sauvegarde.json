{
  "version": "2.0",
  "timestamp": "2024-01-15T14:30:45.123Z",
  "data": {
    "categories": [
      {
        "id": "cat_001",
        "name": "Smartphones",
        "description": "Téléphones portables et accessoires",
        "color": "#2196F3",
        "icon": "phone_android",
        "createdAt": "2024-01-10T10:00:00.000Z",
        "updatedAt": "2024-01-10T10:00:00.000Z"
      },
      {
        "id": "cat_002",
        "name": "Coques de protection",
        "description": "Coques et étuis pour téléphones",
        "color": "#4CAF50",
        "icon": "security",
        "createdAt": "2024-01-10T10:05:00.000Z",
        "updatedAt": "2024-01-10T10:05:00.000Z"
      }
    ],
    "products": [
      {
        "id": "prod_001",
        "name": "iPhone 15 Pro",
        "description": "Smartphone Apple dernière génération",
        "price": 1299.99,
        "cost": 950.00,
        "quantity": 25,
        "minStock": 5,
        "categoryId": "cat_001",
        "barcode": "1234567890123",
        "sku": "IPH15PRO-128",
        "supplier": "Apple Inc.",
        "location": "Étagère A1",
        "weight": 0.187,
        "dimensions": "146.6 x 70.6 x 7.8 mm",
        "color": "Titane naturel",
        "warranty": 24,
        "isActive": true,
        "tags": ["premium", "5G", "nouveau"],
        "images": [],
        "createdAt": "2024-01-12T09:15:00.000Z",
        "updatedAt": "2024-01-14T16:20:00.000Z"
      },
      {
        "id": "prod_002",
        "name": "Coque iPhone 15 Pro Transparente",
        "description": "Coque de protection transparente ultra-fine",
        "price": 29.99,
        "cost": 12.50,
        "quantity": 150,
        "minStock": 20,
        "categoryId": "cat_002",
        "barcode": "2345678901234",
        "sku": "COQ-IPH15-TRANS",
        "supplier": "TechProtect",
        "location": "Étagère B2",
        "weight": 0.025,
        "dimensions": "147 x 71 x 1.2 mm",
        "color": "Transparent",
        "warranty": 12,
        "isActive": true,
        "tags": ["protection", "transparent"],
        "images": [],
        "createdAt": "2024-01-12T10:30:00.000Z",
        "updatedAt": "2024-01-13T14:45:00.000Z"
      }
    ],
    "invoices": [
      {
        "id": "inv_001",
        "invoiceNumber": "FAC-2024-001",
        "customerName": "Jean Dupont",
        "customerEmail": "<EMAIL>",
        "customerPhone": "+33 6 12 34 56 78",
        "customerAddress": "123 Rue de la Paix, 75001 Paris",
        "date": "2024-01-15T14:00:00.000Z",
        "dueDate": "2024-02-15T14:00:00.000Z",
        "status": "paid",
        "paymentMethod": "card",
        "items": [
          {
            "productId": "prod_001",
            "productName": "iPhone 15 Pro",
            "quantity": 1,
            "unitPrice": 1299.99,
            "discount": 0,
            "total": 1299.99
          },
          {
            "productId": "prod_002",
            "productName": "Coque iPhone 15 Pro Transparente",
            "quantity": 2,
            "unitPrice": 29.99,
            "discount": 5.00,
            "total": 54.98
          }
        ],
        "subtotal": 1354.97,
        "taxRate": 20,
        "taxAmount": 270.99,
        "discountAmount": 5.00,
        "totalAmount": 1620.96,
        "notes": "Livraison express demandée",
        "terms": "Paiement à 30 jours",
        "isPaid": true,
        "paidAt": "2024-01-15T14:30:00.000Z",
        "createdAt": "2024-01-15T14:00:00.000Z",
        "updatedAt": "2024-01-15T14:30:00.000Z"
      }
    ],
    "tasks": [
      {
        "id": "task_001",
        "title": "Réapprovisionner iPhone 15 Pro",
        "description": "Commander 20 unités supplémentaires auprès du fournisseur",
        "priority": "high",
        "status": "pending",
        "category": "inventory",
        "assignedTo": "Marie Martin",
        "dueDate": "2024-01-20T09:00:00.000Z",
        "estimatedDuration": 120,
        "tags": ["urgent", "stock"],
        "relatedProductId": "prod_001",
        "createdAt": "2024-01-15T08:00:00.000Z",
        "updatedAt": "2024-01-15T08:00:00.000Z"
      },
      {
        "id": "task_002",
        "title": "Mettre à jour les prix des coques",
        "description": "Réviser les prix de vente des coques de protection",
        "priority": "medium",
        "status": "in_progress",
        "category": "pricing",
        "assignedTo": "Pierre Durand",
        "dueDate": "2024-01-18T17:00:00.000Z",
        "estimatedDuration": 60,
        "tags": ["pricing", "coques"],
        "progress": 50,
        "createdAt": "2024-01-14T14:00:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "colis": [
      {
        "id": "colis_001",
        "trackingNumber": "COL2024001",
        "customerName": "Jean Dupont",
        "customerPhone": "+33 6 12 34 56 78",
        "customerAddress": "123 Rue de la Paix, 75001 Paris",
        "status": "delivered",
        "weight": 0.5,
        "dimensions": "20x15x5 cm",
        "shippingCost": 8.50,
        "carrier": "Colissimo",
        "estimatedDelivery": "2024-01-16T18:00:00.000Z",
        "actualDelivery": "2024-01-16T15:30:00.000Z",
        "items": [
          {
            "productId": "prod_001",
            "productName": "iPhone 15 Pro",
            "quantity": 1
          },
          {
            "productId": "prod_002",
            "productName": "Coque iPhone 15 Pro Transparente",
            "quantity": 2
          }
        ],
        "notes": "Livraison effectuée en main propre",
        "photoPath": "",
        "createdAt": "2024-01-15T14:45:00.000Z",
        "updatedAt": "2024-01-16T15:30:00.000Z"
      }
    ]
  },
  "images": {
    "invoices": {
      "invoice_inv_001_signature": "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA="
