import 'package:flutter/material.dart';
import '../services/diagnostic_service.dart';
import 'mobile_test_page.dart';

class DiagnosticPage extends StatefulWidget {
  const DiagnosticPage({super.key});

  @override
  State<DiagnosticPage> createState() => _DiagnosticPageState();
}

class _DiagnosticPageState extends State<DiagnosticPage> {
  bool _isRunning = false;
  Map<String, dynamic>? _results;

  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunning = true;
      _results = null;
    });

    try {
      final results = await DiagnosticService.runAllTests();
      setState(() {
        _results = results;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _cleanupTestData() async {
    try {
      await DiagnosticService.cleanupTestData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Données de test nettoyées'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du nettoyage: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Diagnostic des Sauvegardes'),
        backgroundColor: Colors.orange[900],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.bug_report, color: Colors.orange[700]),
                        const SizedBox(width: 8),
                        Text(
                          'Tests de Performance',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'Ces tests vérifient si les sauvegardes fonctionnent correctement '
                      'et identifient les problèmes de performance.',
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRunning ? null : _runDiagnostics,
                            icon:
                                _isRunning
                                    ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : const Icon(Icons.play_arrow),
                            label: Text(
                              _isRunning
                                  ? 'Tests en cours...'
                                  : 'Lancer les Tests',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: _cleanupTestData,
                          icon: const Icon(Icons.cleaning_services),
                          label: const Text('Nettoyer'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const MobileTestPage(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.phone_android),
                        label: const Text('Test Mobile Spécifique'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[700],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_results != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.analytics, color: Colors.blue[700]),
                            const SizedBox(width: 8),
                            Text(
                              'Résultats des Tests',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildSummary(),
                        const SizedBox(height: 16),
                        Expanded(
                          child: ListView(
                            children: [
                              _buildTestResult(
                                'Produits',
                                _results!['product'],
                              ),
                              _buildTestResult(
                                'Factures',
                                _results!['invoice'],
                              ),
                              _buildTestResult('Tâches', _results!['task']),
                              _buildTestResult('Colis', _results!['colis']),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummary() {
    final summary = _results!['summary'] as Map<String, dynamic>;
    final allPassed = summary['allPassed'] as bool;
    final successCount = summary['successCount'] as int;
    final totalTests = summary['totalTests'] as int;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: allPassed ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: allPassed ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            allPassed ? Icons.check_circle : Icons.error,
            color: allPassed ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  allPassed
                      ? 'Tous les tests ont réussi !'
                      : 'Certains tests ont échoué',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: allPassed ? Colors.green[800] : Colors.red[800],
                  ),
                ),
                Text(
                  '$successCount/$totalTests tests réussis',
                  style: TextStyle(
                    color: allPassed ? Colors.green[600] : Colors.red[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResult(String title, Map<String, dynamic> result) {
    final success = result['success'] as bool;
    final duration = result['duration'] as int;
    final message = result['message'] as String;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          success ? Icons.check_circle : Icons.error,
          color: success ? Colors.green : Colors.red,
        ),
        title: Text(title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            Text(
              'Durée: ${duration}ms',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            if (!success && result.containsKey('error')) ...[
              const SizedBox(height: 4),
              Text(
                'Erreur: ${result['error']}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.red[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: success ? Colors.green[100] : Colors.red[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            success ? 'OK' : 'ERREUR',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: success ? Colors.green[800] : Colors.red[800],
            ),
          ),
        ),
      ),
    );
  }
}
