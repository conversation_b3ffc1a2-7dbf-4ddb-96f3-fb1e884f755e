import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/colis.dart';
import '../models/invoice.dart';
import '../services/colis_service.dart';
import '../services/invoice_service.dart';

class DeliverySummaryPage extends StatefulWidget {
  const DeliverySummaryPage({super.key});

  @override
  State<DeliverySummaryPage> createState() => _DeliverySummaryPageState();
}

class _DeliverySummaryPageState extends State<DeliverySummaryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ColisService _colisService = ColisService();

  List<Colis> _allColis = [];
  List<Invoice> _allInvoices = [];
  bool _isLoading = true;

  // Filtres
  StatutLivraison? _selectedStatus;
  String? _selectedZone;

  // Formatters
  final _currencyFormat = NumberFormat.currency(
    locale: 'fr_FR',
    symbol: '',
    decimalDigits: 0,
  );
  final _dateFormat = DateFormat('dd/MM/yyyy');

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final colis = await _colisService.loadColis();
      final invoices = await InvoiceService.loadInvoices();

      setState(() {
        _allColis = colis;
        _allInvoices = invoices;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('❌ Erreur chargement données livraisons: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sommaire des Livraisons'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.calendar_view_week), text: 'Cette Semaine'),
            Tab(icon: Icon(Icons.calendar_month), text: 'Ce Mois'),
          ],
        ),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
          PopupMenuButton<StatutLivraison?>(
            icon: const Icon(Icons.filter_list),
            onSelected: (status) {
              setState(() {
                _selectedStatus = status;
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: null,
                    child: Text('Tous les statuts'),
                  ),
                  ...StatutLivraison.values.map(
                    (status) => PopupMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Text(status.emoji),
                          const SizedBox(width: 8),
                          Text(status.libelle),
                        ],
                      ),
                    ),
                  ),
                ],
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [_buildWeeklyView(), _buildMonthlyView()],
              ),
    );
  }

  Widget _buildWeeklyView() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    final weeklyDeliveries = _getDeliveriesInPeriod(startOfWeek, endOfWeek);
    final weeklyInvoices = _getInvoicesInPeriod(startOfWeek, endOfWeek);

    return _buildPeriodView(
      'Cette Semaine',
      '${_dateFormat.format(startOfWeek)} - ${_dateFormat.format(endOfWeek)}',
      weeklyDeliveries,
      weeklyInvoices,
    );
  }

  Widget _buildMonthlyView() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    final monthlyDeliveries = _getDeliveriesInPeriod(startOfMonth, endOfMonth);
    final monthlyInvoices = _getInvoicesInPeriod(startOfMonth, endOfMonth);

    return _buildPeriodView(
      'Ce Mois',
      '${_dateFormat.format(startOfMonth)} - ${_dateFormat.format(endOfMonth)}',
      monthlyDeliveries,
      monthlyInvoices,
    );
  }

  List<Colis> _getDeliveriesInPeriod(DateTime start, DateTime end) {
    return _allColis.where((colis) {
      final isInPeriod =
          colis.dateAjout.isAfter(start.subtract(const Duration(days: 1))) &&
          colis.dateAjout.isBefore(end.add(const Duration(days: 1)));
      final matchesStatus =
          _selectedStatus == null || colis.statut == _selectedStatus;
      final matchesZone =
          _selectedZone == null || colis.zoneLivraison == _selectedZone;

      return isInPeriod && matchesStatus && matchesZone;
    }).toList();
  }

  List<Invoice> _getInvoicesInPeriod(DateTime start, DateTime end) {
    return _allInvoices.where((invoice) {
      return invoice.createdAt.isAfter(
            start.subtract(const Duration(days: 1)),
          ) &&
          invoice.createdAt.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  Widget _buildPeriodView(
    String title,
    String period,
    List<Colis> deliveries,
    List<Invoice> invoices,
  ) {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPeriodHeader(title, period),
            const SizedBox(height: 16),
            _buildStatisticsCards(deliveries, invoices),
            const SizedBox(height: 24),
            _buildStatusBreakdown(deliveries),
            const SizedBox(height: 24),
            _buildZoneBreakdown(deliveries),
            const SizedBox(height: 24),
            _buildDeliveryList(deliveries),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodHeader(String title, String period) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              period,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(List<Colis> deliveries, List<Invoice> invoices) {
    final totalDeliveries = deliveries.length;
    final completedDeliveries =
        deliveries.where((d) => d.statut == StatutLivraison.livree).length;
    final totalRevenue = deliveries.fold<double>(
      0,
      (sum, d) => sum + d.fraisLivraison,
    );

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Livraisons',
            totalDeliveries.toString(),
            Icons.local_shipping,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            'Livrées',
            completedDeliveries.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            'CA Livraisons',
            '${_currencyFormat.format(totalRevenue)} F',
            Icons.monetization_on,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBreakdown(List<Colis> deliveries) {
    final statusCounts = <StatutLivraison, int>{};
    for (final delivery in deliveries) {
      statusCounts[delivery.statut] = (statusCounts[delivery.statut] ?? 0) + 1;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition par Statut',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...StatutLivraison.values.map((status) {
              final count = statusCounts[status] ?? 0;
              final percentage =
                  deliveries.isNotEmpty
                      ? (count / deliveries.length * 100)
                      : 0.0;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Text(status.emoji, style: const TextStyle(fontSize: 20)),
                    const SizedBox(width: 8),
                    Expanded(child: Text(status.libelle)),
                    Text(
                      '$count (${percentage.toStringAsFixed(1)}%)',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildZoneBreakdown(List<Colis> deliveries) {
    final zoneCounts = <String, int>{};
    final zoneRevenue = <String, double>{};

    for (final delivery in deliveries) {
      zoneCounts[delivery.zoneLivraison] =
          (zoneCounts[delivery.zoneLivraison] ?? 0) + 1;
      zoneRevenue[delivery.zoneLivraison] =
          (zoneRevenue[delivery.zoneLivraison] ?? 0) + delivery.fraisLivraison;
    }

    final sortedZones =
        zoneCounts.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top Zones de Livraison',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...sortedZones.take(5).map((entry) {
              final zone = entry.key;
              final count = entry.value;
              final revenue = zoneRevenue[zone] ?? 0;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.location_on, color: Colors.blue),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            zone,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '$count livraisons • ${_currencyFormat.format(revenue)} FCFA',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryList(List<Colis> deliveries) {
    if (deliveries.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(Icons.inbox, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Aucune livraison pour cette période',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    // Trier par date (plus récent en premier)
    final sortedDeliveries = List<Colis>.from(deliveries)
      ..sort((a, b) => b.dateAjout.compareTo(a.dateAjout));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Liste des Livraisons',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  '${deliveries.length} livraison${deliveries.length > 1 ? 's' : ''}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...sortedDeliveries.map((delivery) => _buildDeliveryItem(delivery)),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryItem(Colis delivery) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(delivery.statut.emoji, style: const TextStyle(fontSize: 20)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  delivery.libelle,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(
                    delivery.statut.colorValue,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  delivery.statut.libelle,
                  style: TextStyle(
                    color: Color(delivery.statut.colorValue),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                delivery.zoneLivraison,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(width: 16),
              Icon(Icons.person, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  delivery.nomClient ?? delivery.numeroClient,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                _dateFormat.format(delivery.dateAjout),
                style: TextStyle(color: Colors.grey[600]),
              ),
              const Spacer(),
              Text(
                '${_currencyFormat.format(delivery.fraisLivraison)} FCFA',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          if (delivery.notes != null && delivery.notes!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.note, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      delivery.notes!,
                      style: TextStyle(color: Colors.grey[700], fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
