import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';
import 'data_change_notifier.dart';

/// Service de sauvegarde par chunks pour traiter de gros volumes de données
class ChunkedBackupService {
  static const int _chunkSize = 100; // Nombre d'éléments par chunk
  static const String _backupVersion = '2.1.0';

  /// Créer une sauvegarde par chunks avec barre de progression
  static Future<Map<String, dynamic>> createChunkedBackup({
    Function(double progress, String currentOperation)? onProgress,
  }) async {
    try {
      debugPrint('📦 Création de sauvegarde par chunks...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Initialisation...');

      // 1. Collecter les métadonnées
      final metadata = await _collectMetadata();
      onProgress?.call(0.1, 'Métadonnées collectées');

      // 2. Traiter chaque type de données par chunks
      final chunkedData = <String, dynamic>{};

      // Produits
      onProgress?.call(0.2, 'Traitement des produits...');
      chunkedData['products'] = await _processProductsInChunks(onProgress);

      // Catégories
      onProgress?.call(0.4, 'Traitement des catégories...');
      chunkedData['categories'] = await _processCategoriesInChunks();

      // Factures
      onProgress?.call(0.6, 'Traitement des factures...');
      chunkedData['invoices'] = await _processInvoicesInChunks(onProgress);

      // Tâches
      onProgress?.call(0.8, 'Traitement des tâches...');
      chunkedData['tasks'] = await _processTasksInChunks();

      // Colis
      onProgress?.call(0.9, 'Traitement des colis...');
      chunkedData['colis'] = await _processColisInChunks();

      final backup = {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'chunked': true,
        'metadata': metadata,
        'data': chunkedData,
      };

      stopwatch.stop();
      onProgress?.call(1.0, 'Sauvegarde terminée');
      
      debugPrint('✅ Sauvegarde par chunks créée en ${stopwatch.elapsedMilliseconds}ms');
      return backup;
    } catch (e) {
      debugPrint('❌ Erreur lors de la création de sauvegarde par chunks: $e');
      rethrow;
    }
  }

  /// Collecter les métadonnées
  static Future<Map<String, dynamic>> _collectMetadata() async {
    final results = await Future.wait([
      InventoryService.instance.getProducts(),
      InventoryService.instance.getCategories(),
      InvoiceService.loadInvoices(),
      TaskService.instance.getTasks(),
      ColisService.instance.getAllColis(),
    ]);

    return {
      'totalProducts': (results[0] as List).length,
      'totalCategories': (results[1] as List).length,
      'totalInvoices': (results[2] as List).length,
      'totalTasks': (results[3] as List).length,
      'totalColis': (results[4] as List).length,
      'chunkSize': _chunkSize,
    };
  }

  /// Traiter les produits par chunks
  static Future<List<List<Map<String, dynamic>>>> _processProductsInChunks(
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final products = await InventoryService.instance.getProducts();
    final chunks = <List<Map<String, dynamic>>>[];
    
    for (int i = 0; i < products.length; i += _chunkSize) {
      final end = (i + _chunkSize < products.length) ? i + _chunkSize : products.length;
      final chunk = products.sublist(i, end);
      
      // Optimiser chaque produit dans le chunk
      final optimizedChunk = chunk.map((product) {
        final json = product.toJson();
        // Supprimer les champs vides pour réduire la taille
        json.removeWhere((key, value) => 
          value == null || 
          (value is String && value.isEmpty) ||
          (value is List && value.isEmpty)
        );
        return json;
      }).toList();
      
      chunks.add(optimizedChunk);
      
      // Mettre à jour la progression
      final progress = 0.2 + (0.2 * (i + chunk.length) / products.length);
      onProgress?.call(progress, 'Produits: ${i + chunk.length}/${products.length}');
      
      // Petite pause pour ne pas bloquer l'UI
      await Future.delayed(const Duration(milliseconds: 10));
    }
    
    debugPrint('✅ ${products.length} produits traités en ${chunks.length} chunks');
    return chunks;
  }

  /// Traiter les catégories par chunks
  static Future<List<List<Map<String, dynamic>>>> _processCategoriesInChunks() async {
    final categories = await InventoryService.instance.getCategories();
    final chunks = <List<Map<String, dynamic>>>[];
    
    for (int i = 0; i < categories.length; i += _chunkSize) {
      final end = (i + _chunkSize < categories.length) ? i + _chunkSize : categories.length;
      final chunk = categories.sublist(i, end);
      
      final optimizedChunk = chunk.map((category) {
        final json = category.toJson();
        json.removeWhere((key, value) => value == null || (value is String && value.isEmpty));
        return json;
      }).toList();
      
      chunks.add(optimizedChunk);
    }
    
    debugPrint('✅ ${categories.length} catégories traitées en ${chunks.length} chunks');
    return chunks;
  }

  /// Traiter les factures par chunks
  static Future<List<List<Map<String, dynamic>>>> _processInvoicesInChunks(
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final invoices = await InvoiceService.loadInvoices();
    final chunks = <List<Map<String, dynamic>>>[];
    
    for (int i = 0; i < invoices.length; i += _chunkSize) {
      final end = (i + _chunkSize < invoices.length) ? i + _chunkSize : invoices.length;
      final chunk = invoices.sublist(i, end);
      
      final optimizedChunk = chunk.map((invoice) {
        final json = invoice.toJson();
        // Supprimer les images trop volumineuses
        if (json['imageBase64'] != null && (json['imageBase64'] as String).length > 50000) {
          json.remove('imageBase64');
        }
        json.removeWhere((key, value) => 
          value == null || 
          (value is String && value.isEmpty) ||
          (value is List && value.isEmpty)
        );
        return json;
      }).toList();
      
      chunks.add(optimizedChunk);
      
      // Mettre à jour la progression
      final progress = 0.6 + (0.2 * (i + chunk.length) / invoices.length);
      onProgress?.call(progress, 'Factures: ${i + chunk.length}/${invoices.length}');
      
      await Future.delayed(const Duration(milliseconds: 10));
    }
    
    debugPrint('✅ ${invoices.length} factures traitées en ${chunks.length} chunks');
    return chunks;
  }

  /// Traiter les tâches par chunks
  static Future<List<List<Map<String, dynamic>>>> _processTasksInChunks() async {
    final tasks = await TaskService.instance.getTasks();
    final chunks = <List<Map<String, dynamic>>>[];
    
    for (int i = 0; i < tasks.length; i += _chunkSize) {
      final end = (i + _chunkSize < tasks.length) ? i + _chunkSize : tasks.length;
      final chunk = tasks.sublist(i, end);
      
      final optimizedChunk = chunk.map((task) {
        final json = task.toJson();
        json.removeWhere((key, value) => value == null || (value is String && value.isEmpty));
        return json;
      }).toList();
      
      chunks.add(optimizedChunk);
    }
    
    debugPrint('✅ ${tasks.length} tâches traitées en ${chunks.length} chunks');
    return chunks;
  }

  /// Traiter les colis par chunks
  static Future<List<List<Map<String, dynamic>>>> _processColisInChunks() async {
    final colis = await ColisService.instance.getAllColis();
    final chunks = <List<Map<String, dynamic>>>[];
    
    for (int i = 0; i < colis.length; i += _chunkSize) {
      final end = (i + _chunkSize < colis.length) ? i + _chunkSize : colis.length;
      final chunk = colis.sublist(i, end);
      
      final optimizedChunk = chunk.map((c) {
        final json = c.toJson();
        json.removeWhere((key, value) => value == null || (value is String && value.isEmpty));
        return json;
      }).toList();
      
      chunks.add(optimizedChunk);
    }
    
    debugPrint('✅ ${colis.length} colis traités en ${chunks.length} chunks');
    return chunks;
  }

  /// Restaurer depuis une sauvegarde par chunks
  static Future<void> restoreFromChunkedBackup(
    Map<String, dynamic> backup, {
    Function(double progress, String currentOperation)? onProgress,
  }) async {
    try {
      debugPrint('📂 Restauration depuis sauvegarde par chunks...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Initialisation de la restauration...');

      final data = backup['data'] as Map<String, dynamic>;
      final metadata = backup['metadata'] as Map<String, dynamic>;

      // Restaurer chaque type de données
      await _restoreProductsFromChunks(
        data['products'] as List<dynamic>? ?? [],
        metadata['totalProducts'] as int? ?? 0,
        onProgress,
      );

      await _restoreCategoriesFromChunks(
        data['categories'] as List<dynamic>? ?? [],
        onProgress,
      );

      await _restoreInvoicesFromChunks(
        data['invoices'] as List<dynamic>? ?? [],
        metadata['totalInvoices'] as int? ?? 0,
        onProgress,
      );

      await _restoreTasksFromChunks(
        data['tasks'] as List<dynamic>? ?? [],
        onProgress,
      );

      await _restoreColisFromChunks(
        data['colis'] as List<dynamic>? ?? [],
        onProgress,
      );

      // Notifier les changements
      DataChangeNotifier.instance.notifyAllDataChanged();

      stopwatch.stop();
      onProgress?.call(1.0, 'Restauration terminée');
      debugPrint('✅ Restauration par chunks terminée en ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration par chunks: $e');
      rethrow;
    }
  }

  /// Restaurer les produits depuis les chunks
  static Future<void> _restoreProductsFromChunks(
    List<dynamic> chunks,
    int totalProducts,
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final allProducts = <Product>[];
    int processedCount = 0;

    for (int i = 0; i < chunks.length; i++) {
      final chunk = chunks[i] as List<dynamic>;
      
      for (final productData in chunk) {
        try {
          final product = Product.fromJson(productData as Map<String, dynamic>);
          allProducts.add(product);
          processedCount++;
        } catch (e) {
          debugPrint('⚠️ Erreur lors de la restauration du produit: $e');
        }
      }

      // Mettre à jour la progression
      final progress = 0.1 + (0.3 * processedCount / totalProducts);
      onProgress?.call(progress, 'Produits restaurés: $processedCount/$totalProducts');
      
      await Future.delayed(const Duration(milliseconds: 5));
    }

    // Sauvegarder tous les produits
    final prefs = await SharedPreferences.getInstance();
    final productsJson = jsonEncode(allProducts.map((p) => p.toJson()).toList());
    await prefs.setString('inventory_products', productsJson);
    
    debugPrint('✅ ${allProducts.length} produits restaurés depuis ${chunks.length} chunks');
  }

  /// Restaurer les catégories depuis les chunks
  static Future<void> _restoreCategoriesFromChunks(
    List<dynamic> chunks,
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final allCategories = <Category>[];

    for (final chunk in chunks) {
      final chunkList = chunk as List<dynamic>;
      for (final categoryData in chunkList) {
        try {
          final category = Category.fromJson(categoryData as Map<String, dynamic>);
          allCategories.add(category);
        } catch (e) {
          debugPrint('⚠️ Erreur lors de la restauration de la catégorie: $e');
        }
      }
    }

    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(allCategories.map((c) => c.toJson()).toList());
    await prefs.setString('inventory_categories', categoriesJson);
    
    onProgress?.call(0.5, 'Catégories restaurées: ${allCategories.length}');
    debugPrint('✅ ${allCategories.length} catégories restaurées');
  }

  /// Restaurer les factures depuis les chunks
  static Future<void> _restoreInvoicesFromChunks(
    List<dynamic> chunks,
    int totalInvoices,
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final allInvoices = <Invoice>[];
    int processedCount = 0;

    for (final chunk in chunks) {
      final chunkList = chunk as List<dynamic>;
      for (final invoiceData in chunkList) {
        try {
          final invoice = Invoice.fromJson(invoiceData as Map<String, dynamic>);
          allInvoices.add(invoice);
          processedCount++;
        } catch (e) {
          debugPrint('⚠️ Erreur lors de la restauration de la facture: $e');
        }
      }

      final progress = 0.6 + (0.2 * processedCount / totalInvoices);
      onProgress?.call(progress, 'Factures restaurées: $processedCount/$totalInvoices');
      
      await Future.delayed(const Duration(milliseconds: 5));
    }

    await InvoiceService.saveInvoices(allInvoices);
    debugPrint('✅ ${allInvoices.length} factures restaurées');
  }

  /// Restaurer les tâches depuis les chunks
  static Future<void> _restoreTasksFromChunks(
    List<dynamic> chunks,
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final allTasks = <Task>[];

    for (final chunk in chunks) {
      final chunkList = chunk as List<dynamic>;
      for (final taskData in chunkList) {
        try {
          final task = Task.fromJson(taskData as Map<String, dynamic>);
          allTasks.add(task);
        } catch (e) {
          debugPrint('⚠️ Erreur lors de la restauration de la tâche: $e');
        }
      }
    }

    final prefs = await SharedPreferences.getInstance();
    final tasksJson = jsonEncode(allTasks.map((t) => t.toJson()).toList());
    await prefs.setString('tasks', tasksJson);
    
    onProgress?.call(0.9, 'Tâches restaurées: ${allTasks.length}');
    debugPrint('✅ ${allTasks.length} tâches restaurées');
  }

  /// Restaurer les colis depuis les chunks
  static Future<void> _restoreColisFromChunks(
    List<dynamic> chunks,
    Function(double progress, String currentOperation)? onProgress,
  ) async {
    final allColis = <Colis>[];

    for (final chunk in chunks) {
      final chunkList = chunk as List<dynamic>;
      for (final colisData in chunkList) {
        try {
          final colis = Colis.fromJson(colisData as Map<String, dynamic>);
          allColis.add(colis);
        } catch (e) {
          debugPrint('⚠️ Erreur lors de la restauration du colis: $e');
        }
      }
    }

    await ColisService.instance.saveColis(allColis);
    onProgress?.call(0.95, 'Colis restaurés: ${allColis.length}');
    debugPrint('✅ ${allColis.length} colis restaurés');
  }
}
