import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/invoice.dart';
import 'invoice_service.dart';

/// Service de gamification pour motiver les employés
class GamificationService {
  static final GamificationService _instance = GamificationService._internal();
  factory GamificationService() => _instance;
  GamificationService._internal();

  static GamificationService get instance => _instance;

  // Clés de stockage
  static const String _totalPointsKey = 'gamification_total_points';
  static const String _levelKey = 'gamification_level';
  static const String _badgesKey = 'gamification_badges';
  static const String _streakKey = 'gamification_streak';
  static const String _lastInvoiceDateKey = 'gamification_last_invoice_date';

  /// Obtenir les statistiques de gamification actuelles
  Future<GamificationStats> getGamificationStats() async {
    final prefs = await SharedPreferences.getInstance();

    final totalPoints = prefs.getInt(_totalPointsKey) ?? 0;
    final level = prefs.getInt(_levelKey) ?? 1;
    final badgesList = prefs.getStringList(_badgesKey) ?? [];
    final streak = prefs.getInt(_streakKey) ?? 0;
    final lastInvoiceDate = prefs.getString(_lastInvoiceDateKey);

    // Calculer les statistiques du mois en cours
    final monthlyStats = await _calculateMonthlyStats();

    return GamificationStats(
      totalPoints: totalPoints,
      level: level,
      badges: badgesList.map((id) => Badge.fromId(id)).toList(),
      currentStreak: streak,
      monthlyInvoices: monthlyStats['invoices'],
      monthlyRevenue: monthlyStats['revenue'],
      monthlyClients: monthlyStats['clients'],
      lastInvoiceDate:
          lastInvoiceDate != null ? DateTime.parse(lastInvoiceDate) : null,
    );
  }

  /// Traiter l'ajout d'une nouvelle facture et calculer les récompenses
  Future<GamificationReward> processNewInvoice(Invoice invoice) async {
    final prefs = await SharedPreferences.getInstance();

    // Points de base pour une facture
    int pointsEarned = 10;
    List<String> messages = ['🎉 Nouvelle commande enregistrée !'];
    List<Badge> newBadges = [];
    bool levelUp = false;

    // Bonus selon le montant de la facture
    final subtotal = InvoiceService().calculateSubtotal(invoice.items);
    if (subtotal >= 10000) {
      pointsEarned += 20;
      messages.add('💰 Grosse commande ! +20 points bonus');
    } else if (subtotal >= 5000) {
      pointsEarned += 10;
      messages.add('💵 Belle commande ! +10 points bonus');
    }

    // Bonus pour nouveau client
    if (await _isNewClient(invoice.clientName, invoice.clientNumber)) {
      pointsEarned += 15;
      messages.add('🆕 Nouveau client ! +15 points bonus');
    }

    // Mettre à jour les points totaux
    final currentPoints = prefs.getInt(_totalPointsKey) ?? 0;
    final newTotalPoints = currentPoints + pointsEarned;
    await prefs.setInt(_totalPointsKey, newTotalPoints);

    // Vérifier le niveau
    final currentLevel = prefs.getInt(_levelKey) ?? 1;
    final newLevel = _calculateLevel(newTotalPoints);
    if (newLevel > currentLevel) {
      await prefs.setInt(_levelKey, newLevel);
      levelUp = true;
      messages.add('🌟 NIVEAU $newLevel ATTEINT ! Félicitations !');
    }

    // Mettre à jour la série (streak)
    final newStreak = await _updateStreak();
    if (newStreak > 0 && newStreak % 5 == 0) {
      pointsEarned += newStreak;
      messages.add('🔥 Série de $newStreak jours ! +$newStreak points bonus');
    }

    // Vérifier les nouveaux badges
    newBadges = await _checkForNewBadges();
    if (newBadges.isNotEmpty) {
      for (final badge in newBadges) {
        messages.add('🏆 Badge débloqué : ${badge.name}');
      }
    }

    // Mettre à jour la date de dernière facture
    await prefs.setString(
      _lastInvoiceDateKey,
      DateTime.now().toIso8601String(),
    );

    return GamificationReward(
      pointsEarned: pointsEarned,
      messages: messages,
      newBadges: newBadges,
      levelUp: levelUp,
      newLevel: newLevel,
      currentStreak: newStreak,
    );
  }

  /// Calculer le niveau basé sur les points (seuils ajustés)
  int _calculateLevel(int points) {
    // Seuils plus accessibles pour encourager la progression
    if (points < 50) return 1; // Débutant
    if (points < 150) return 2; // Apprenti
    if (points < 300) return 3; // Actif
    if (points < 500) return 4; // Motivé
    if (points < 750) return 5; // Expérimenté
    if (points < 1100) return 6; // Expert
    if (points < 1500) return 7; // Professionnel
    if (points < 2000) return 8; // Champion
    if (points < 2600) return 9; // Maître
    return 10; // Légende
  }

  /// Calculer les points nécessaires pour le prochain niveau
  int getPointsForNextLevel(int currentLevel) {
    switch (currentLevel) {
      case 1:
        return 50;
      case 2:
        return 150;
      case 3:
        return 300;
      case 4:
        return 500;
      case 5:
        return 750;
      case 6:
        return 1100;
      case 7:
        return 1500;
      case 8:
        return 2000;
      case 9:
        return 2600;
      default:
        return 2600; // Niveau max atteint
    }
  }

  /// Vérifier si c'est un nouveau client
  Future<bool> _isNewClient(String clientName, String clientNumber) async {
    final invoices = await InvoiceService.loadInvoicesLocal();
    final existingInvoices =
        invoices
            .where(
              (inv) =>
                  inv.clientName == clientName &&
                  inv.clientNumber == clientNumber,
            )
            .toList();
    return existingInvoices.length <= 1; // Première facture pour ce client
  }

  /// Mettre à jour la série de jours consécutifs
  Future<int> _updateStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final lastDateStr = prefs.getString(_lastInvoiceDateKey);
    final currentStreak = prefs.getInt(_streakKey) ?? 0;

    if (lastDateStr == null) {
      await prefs.setInt(_streakKey, 1);
      return 1;
    }

    final lastDate = DateTime.parse(lastDateStr);
    final today = DateTime.now();
    final daysDifference = today.difference(lastDate).inDays;

    int newStreak;
    if (daysDifference == 1) {
      // Jour consécutif
      newStreak = currentStreak + 1;
    } else if (daysDifference == 0) {
      // Même jour
      newStreak = currentStreak;
    } else {
      // Série cassée
      newStreak = 1;
    }

    await prefs.setInt(_streakKey, newStreak);
    return newStreak;
  }

  /// Vérifier les nouveaux badges débloqués
  Future<List<Badge>> _checkForNewBadges() async {
    final prefs = await SharedPreferences.getInstance();
    final currentBadges = prefs.getStringList(_badgesKey) ?? [];
    final stats = await getGamificationStats();
    final monthlyStats = await _calculateMonthlyStats();
    final invoices = await InvoiceService.loadInvoicesLocal();
    final now = DateTime.now();

    List<Badge> newBadges = [];

    // Badge première vente
    if (!currentBadges.contains('first_sale') && stats.totalPoints > 0) {
      newBadges.add(Badge.firstSale);
    }

    // Badge 10 factures
    if (!currentBadges.contains('ten_invoices') &&
        monthlyStats['invoices'] >= 10) {
      newBadges.add(Badge.tenInvoices);
    }

    // Badge grosse vente
    if (!currentBadges.contains('big_sale')) {
      final hasBigSale = invoices.any(
        (inv) => InvoiceService().calculateSubtotal(inv.items) >= 20000,
      );
      if (hasBigSale) {
        newBadges.add(Badge.bigSale);
      }
    }

    // Badge série de 7 jours
    if (!currentBadges.contains('week_streak') && stats.currentStreak >= 7) {
      newBadges.add(Badge.weekStreak);
    }

    // Badge niveau 5
    if (!currentBadges.contains('level_five') && stats.level >= 5) {
      newBadges.add(Badge.levelFive);
    }

    // Badge objectif mensuel (200 clients valides)
    if (!currentBadges.contains('monthly_target') &&
        monthlyStats['validClients'] >= 200) {
      newBadges.add(Badge.monthlyTarget);
    }

    // Badge rapidité (5 factures en une journée)
    if (!currentBadges.contains('speedster')) {
      final today = DateTime(now.year, now.month, now.day);
      final todayInvoices =
          invoices
              .where(
                (inv) =>
                    inv.createdAt.isAfter(today) &&
                    inv.createdAt.isBefore(today.add(const Duration(days: 1))),
              )
              .length;
      if (todayInvoices >= 5) {
        newBadges.add(Badge.speedster);
      }
    }

    // Badge régularité (30 jours consécutifs)
    if (!currentBadges.contains('consistent') && stats.currentStreak >= 30) {
      newBadges.add(Badge.consistent);
    }

    // Badge gros client (client avec plus de 50k FCFA)
    if (!currentBadges.contains('big_spender')) {
      final hasLargeClient = _hasClientWithAmount(invoices, 50000);
      if (hasLargeClient) {
        newBadges.add(Badge.bigSpender);
      }
    }

    // Badge réseau social (50 clients différents)
    if (!currentBadges.contains('socializer') &&
        monthlyStats['clients'] >= 50) {
      newBadges.add(Badge.socializer);
    }

    // Badge lève-tôt (facture avant 8h)
    if (!currentBadges.contains('early_bird')) {
      final hasEarlyInvoice = invoices.any((inv) => inv.createdAt.hour < 8);
      if (hasEarlyInvoice) {
        newBadges.add(Badge.earlyBird);
      }
    }

    // Badge couche-tard (facture après 20h)
    if (!currentBadges.contains('night_owl')) {
      final hasLateInvoice = invoices.any((inv) => inv.createdAt.hour >= 20);
      if (hasLateInvoice) {
        newBadges.add(Badge.nightOwl);
      }
    }

    // Badge semaine parfaite (3+ factures chaque jour de la semaine)
    if (!currentBadges.contains('perfect_week')) {
      final hasPerfectWeek = _checkPerfectWeek(invoices);
      if (hasPerfectWeek) {
        newBadges.add(Badge.perfectWeek);
      }
    }

    // Badge retour en force (retour après 7+ jours de pause)
    if (!currentBadges.contains('comeback')) {
      final hasComeback = _checkComeback(invoices);
      if (hasComeback) {
        newBadges.add(Badge.comeback);
      }
    }

    // Sauvegarder les nouveaux badges
    if (newBadges.isNotEmpty) {
      final updatedBadges = [...currentBadges, ...newBadges.map((b) => b.id)];
      await prefs.setStringList(_badgesKey, updatedBadges);
    }

    return newBadges;
  }

  /// Calculer les statistiques du mois en cours
  Future<Map<String, dynamic>> _calculateMonthlyStats() async {
    final invoices = await InvoiceService.loadInvoicesLocal();
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);

    final monthlyInvoices =
        invoices
            .where(
              (inv) =>
                  inv.createdAt.isAfter(startOfMonth) &&
                  inv.status == InvoiceStatus.payee,
            )
            .toList();

    final revenue = monthlyInvoices.fold<double>(
      0,
      (sum, inv) => sum + InvoiceService().calculateSubtotal(inv.items),
    );

    final uniqueClients =
        monthlyInvoices
            .map((inv) => '${inv.clientName}_${inv.clientNumber}')
            .toSet()
            .length;

    return {
      'invoices': monthlyInvoices.length,
      'revenue': revenue,
      'clients': uniqueClients,
    };
  }

  /// Vérifier si un client a dépensé un montant minimum
  bool _hasClientWithAmount(List<Invoice> invoices, double minAmount) {
    Map<String, double> clientTotals = {};

    for (final invoice in invoices) {
      if (invoice.status == InvoiceStatus.payee) {
        final clientKey = '${invoice.clientName}_${invoice.clientNumber}';
        final subtotal = InvoiceService().calculateSubtotal(invoice.items);
        clientTotals[clientKey] = (clientTotals[clientKey] ?? 0) + subtotal;
      }
    }

    return clientTotals.values.any((total) => total >= minAmount);
  }

  /// Vérifier si l'employé a eu une semaine parfaite
  bool _checkPerfectWeek(List<Invoice> invoices) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    // Compter les factures par jour de la semaine
    Map<int, int> dailyCounts = {};

    for (final invoice in invoices) {
      if (invoice.createdAt.isAfter(startOfWeek) &&
          invoice.createdAt.isBefore(now) &&
          invoice.status == InvoiceStatus.payee) {
        final dayOfWeek = invoice.createdAt.weekday;
        dailyCounts[dayOfWeek] = (dailyCounts[dayOfWeek] ?? 0) + 1;
      }
    }

    // Vérifier si chaque jour de la semaine a au moins 3 factures
    for (int day = 1; day <= 7; day++) {
      if ((dailyCounts[day] ?? 0) < 3) {
        return false;
      }
    }

    return true;
  }

  /// Vérifier si l'employé fait un retour après une pause
  bool _checkComeback(List<Invoice> invoices) {
    if (invoices.length < 2) return false;

    final sortedInvoices =
        invoices.where((inv) => inv.status == InvoiceStatus.payee).toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    if (sortedInvoices.length < 2) return false;

    final latest = sortedInvoices[0];
    final previous = sortedInvoices[1];

    // Vérifier s'il y a eu une pause de 7+ jours
    final daysBetween = latest.createdAt.difference(previous.createdAt).inDays;
    return daysBetween >= 7;
  }

  /// Obtenir un message d'encouragement aléatoire
  String getRandomEncouragement() {
    final encouragements = [
      "💪 Continue comme ça !",
      "🚀 Tu es sur la bonne voie !",
      "⭐ Excellent travail !",
      "🎯 Objectif en vue !",
      "🔥 Tu assures !",
      "💎 Performance de champion !",
      "🌟 Brillant !",
      "🏆 Tu es le meilleur !",
      "💯 Performance parfaite !",
      "🎉 Fantastique !",
    ];
    return encouragements[Random().nextInt(encouragements.length)];
  }

  /// Réinitialiser les statistiques (pour les tests)
  Future<void> resetStats() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_totalPointsKey);
    await prefs.remove(_levelKey);
    await prefs.remove(_badgesKey);
    await prefs.remove(_streakKey);
    await prefs.remove(_lastInvoiceDateKey);
  }
}

/// Modèle pour les statistiques de gamification
class GamificationStats {
  final int totalPoints;
  final int level;
  final List<Badge> badges;
  final int currentStreak;
  final int monthlyInvoices;
  final double monthlyRevenue;
  final int monthlyClients;
  final DateTime? lastInvoiceDate;

  const GamificationStats({
    required this.totalPoints,
    required this.level,
    required this.badges,
    required this.currentStreak,
    required this.monthlyInvoices,
    required this.monthlyRevenue,
    required this.monthlyClients,
    this.lastInvoiceDate,
  });
}

/// Modèle pour les récompenses gagnées
class GamificationReward {
  final int pointsEarned;
  final List<String> messages;
  final List<Badge> newBadges;
  final bool levelUp;
  final int newLevel;
  final int currentStreak;

  const GamificationReward({
    required this.pointsEarned,
    required this.messages,
    required this.newBadges,
    required this.levelUp,
    required this.newLevel,
    required this.currentStreak,
  });
}

/// Modèle pour les badges
class Badge {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
  });

  static const Badge firstSale = Badge(
    id: 'first_sale',
    name: 'Première Vente',
    description: 'Votre première facture enregistrée',
    icon: Icons.star,
    color: Colors.amber,
  );

  static const Badge tenInvoices = Badge(
    id: 'ten_invoices',
    name: 'Vendeur Actif',
    description: '10 factures ce mois-ci',
    icon: Icons.trending_up,
    color: Colors.green,
  );

  static const Badge bigSale = Badge(
    id: 'big_sale',
    name: 'Grosse Vente',
    description: 'Vente de plus de 20 000 FCFA',
    icon: Icons.diamond,
    color: Colors.purple,
  );

  static const Badge weekStreak = Badge(
    id: 'week_streak',
    name: 'Série Hebdomadaire',
    description: '7 jours consécutifs de ventes',
    icon: Icons.local_fire_department,
    color: Colors.orange,
  );

  static const Badge levelFive = Badge(
    id: 'level_five',
    name: 'Expert Vendeur',
    description: 'Niveau 5 atteint',
    icon: Icons.military_tech,
    color: Colors.blue,
  );

  static const Badge monthlyTarget = Badge(
    id: 'monthly_target',
    name: 'Objectif Mensuel',
    description: '200 clients valides ce mois-ci',
    icon: Icons.flag,
    color: Colors.green,
  );

  static const Badge speedster = Badge(
    id: 'speedster',
    name: 'Rapide comme l\'éclair',
    description: '5 factures en une journée',
    icon: Icons.flash_on,
    color: Colors.yellow,
  );

  static const Badge consistent = Badge(
    id: 'consistent',
    name: 'Régularité',
    description: '30 jours consécutifs de ventes',
    icon: Icons.calendar_today,
    color: Colors.teal,
  );

  static const Badge bigSpender = Badge(
    id: 'big_spender',
    name: 'Gros Client',
    description: 'Client avec plus de 50 000 FCFA',
    icon: Icons.account_balance,
    color: Colors.deepPurple,
  );

  static const Badge socializer = Badge(
    id: 'socializer',
    name: 'Réseau Social',
    description: '50 clients différents',
    icon: Icons.group_add,
    color: Colors.cyan,
  );

  static const Badge earlyBird = Badge(
    id: 'early_bird',
    name: 'Lève-tôt',
    description: 'Première facture avant 8h',
    icon: Icons.wb_sunny,
    color: Colors.orange,
  );

  static const Badge nightOwl = Badge(
    id: 'night_owl',
    name: 'Couche-tard',
    description: 'Facture après 20h',
    icon: Icons.nightlight,
    color: Colors.indigo,
  );

  static const Badge perfectWeek = Badge(
    id: 'perfect_week',
    name: 'Semaine Parfaite',
    description: 'Au moins 3 factures chaque jour de la semaine',
    icon: Icons.star_border,
    color: Colors.amber,
  );

  static const Badge comeback = Badge(
    id: 'comeback',
    name: 'Retour en Force',
    description: 'Retour après une pause de 7+ jours',
    icon: Icons.refresh,
    color: Colors.lightGreen,
  );

  static Badge fromId(String id) {
    switch (id) {
      case 'first_sale':
        return firstSale;
      case 'ten_invoices':
        return tenInvoices;
      case 'big_sale':
        return bigSale;
      case 'week_streak':
        return weekStreak;
      case 'level_five':
        return levelFive;
      case 'monthly_target':
        return monthlyTarget;
      case 'speedster':
        return speedster;
      case 'consistent':
        return consistent;
      case 'big_spender':
        return bigSpender;
      case 'socializer':
        return socializer;
      case 'early_bird':
        return earlyBird;
      case 'night_owl':
        return nightOwl;
      case 'perfect_week':
        return perfectWeek;
      case 'comeback':
        return comeback;
      default:
        return firstSale;
    }
  }
}
