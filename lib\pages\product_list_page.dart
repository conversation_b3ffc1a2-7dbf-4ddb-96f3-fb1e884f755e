import 'package:flutter/material.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart';
import 'package:general_hcp_crm/services/inventory_service.dart';
import '../utils/performance_optimizer.dart';
import '../widgets/adaptive_grid_view.dart';
import '../services/data_change_notifier.dart';
import '../services/theme_service.dart';
import 'add_edit_product_page.dart';
import 'import_export_page.dart';

class ProductListPage extends StatefulWidget {
  const ProductListPage({super.key});

  @override
  State<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage>
    with PerformanceOptimizedState, DataChangeListener {
  final InventoryService _inventoryService = InventoryService.instance;
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  List<Category> _categories = [];
  Category? _selectedCategory;
  String _searchTerm = '';
  bool _isGridView = false; // Pour basculer entre vue liste et grille

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void onDataChanged() {
    // Recharger les données quand elles changent
    debugPrint(
      '🔄 ProductListPage: Rechargement des données suite à un changement',
    );
    _loadData();
  }

  Future<void> _loadData() async {
    // Afficher immédiatement avec des données par défaut
    if (mounted) {
      setState(() {
        _products = [];
        _categories = [];
        _filteredProducts = [];
      });
    }

    // Charger les vraies données en arrière-plan
    try {
      final products = await _inventoryService.getProducts();
      final categories = await _inventoryService.getCategories();

      if (mounted) {
        setState(() {
          _products = products;
          _categories = categories;
        });
        _applyFilters();
      }
    } catch (e) {
      // Les données par défaut restent affichées
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur de chargement des données: ${e.toString()}'),
          ),
        );
      }
    }
  }

  void _applyFilters() {
    _filteredProducts =
        _products.where((product) {
          final matchesCategory =
              _selectedCategory == null ||
              product.categoryId == _selectedCategory!.id;
          final matchesSearchTerm =
              _searchTerm.isEmpty ||
              product.name.toLowerCase().contains(_searchTerm.toLowerCase());
          return matchesCategory && matchesSearchTerm;
        }).toList();
    safeSetState(() {});
  }

  void _onSearchChanged(String term) {
    _searchTerm = term;
    // Débounce pour éviter trop d'appels lors de la saisie
    debouncedOperation(() => _applyFilters());
  }

  void _onCategoryChanged(Category? category) {
    _selectedCategory = category;
    _applyFilters();
  }

  void _toggleView() {
    safeSetState(() {
      _isGridView = !_isGridView;
    });
  }

  void _navigateToAddProduct() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditProductPage()),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si un produit a été ajouté/modifié
    }
  }

  void _navigateToImportExport() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ImportExportPage()),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si des produits ont été importés
    }
  }

  void _navigateToEditProduct(Product product) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProductPage(product: product),
      ),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si un produit a été ajouté/modifié
    }
  }

  Future<void> _deleteProduct(Product product) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Voulez-vous vraiment supprimer le produit "${product.name}" ?',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        await _inventoryService.deleteProduct(product.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Produit "${product.name}" supprimé.')),
          );
        }
        if (mounted) {
          _loadData();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: ${e.toString()}'),
            ),
          );
        }
      }
    }
  }

  void _showAddCategoryDialog() {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ajouter une catégorie'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nom de la catégorie',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Veuillez entrer un nom de catégorie';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: priceController,
                    decoration: const InputDecoration(
                      labelText: 'Prix par défaut (optionnel)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.euro),
                      suffixText: '€',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final price = double.tryParse(value);
                        if (price == null || price < 0) {
                          return 'Veuillez entrer un prix valide';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    final defaultPrice =
                        priceController.text.isNotEmpty
                            ? double.tryParse(priceController.text)
                            : null;

                    final newCategory = Category(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      defaultPrice: defaultPrice,
                    );

                    try {
                      await _inventoryService.addCategory(newCategory);
                      if (mounted) {
                        navigator.pop();
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              'Catégorie "${newCategory.name}" ajoutée.',
                            ),
                          ),
                        );
                        if (mounted) {
                          _loadData(); // Recharger les données pour mettre à jour la liste des catégories
                        }
                      }
                    } catch (e) {
                      if (mounted) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              'Erreur lors de l\'ajout: ${e.toString()}',
                            ),
                          ),
                        );
                      }
                    }
                  }
                },
                child: const Text('Ajouter'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Obtenir le thème dynamique pour les produits
    final themeService = ThemeService.instance;
    final pageTheme = themeService.getThemeForPage('products');

    // Appliquer le thème système
    WidgetsBinding.instance.addPostFrameCallback((_) {
      themeService.applySystemUITheme(pageTheme);
    });

    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icône des produits avec animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.inventory_2,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            // Titre avec animation
            const AnimatedDefaultTextStyle(
              duration: Duration(milliseconds: 300),
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              child: Text('HCP-DESIGN - Inventaire'),
            ),
          ],
        ),
        backgroundColor: pageTheme.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        centerTitle: true,
        shadowColor: pageTheme.secondary.withValues(alpha: 0.5),

        // Gradient en arrière-plan
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [pageTheme.primary, pageTheme.secondary],
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.import_export),
            onPressed: _navigateToImportExport,
            tooltip: 'Import / Export',
          ),
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: _toggleView,
            tooltip: _isGridView ? 'Vue Liste' : 'Vue Grille',
          ),
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: _showAddCategoryDialog,
            tooltip: 'Ajouter une catégorie',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child:
                _filteredProducts.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                    ? _buildProductGridView()
                    : _buildProductListView(),
          ),
        ],
      ),
      floatingActionButton: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        child: FloatingActionButton(
          onPressed: _navigateToAddProduct,
          tooltip: 'Ajouter un produit',
          backgroundColor: pageTheme.accent,
          foregroundColor: Colors.white,
          elevation: 6,
          highlightElevation: 12,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                _searchTerm.isNotEmpty || _selectedCategory != null
                    ? Icons.search_off
                    : Icons.inventory_2_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _searchTerm.isNotEmpty || _selectedCategory != null
                  ? 'Aucun produit trouvé'
                  : 'Aucun produit disponible',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchTerm.isNotEmpty || _selectedCategory != null
                  ? 'Essayez de modifier vos critères de recherche'
                  : 'Commencez par ajouter votre premier produit',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (_searchTerm.isEmpty && _selectedCategory == null)
              ElevatedButton.icon(
                onPressed: _navigateToAddProduct,
                icon: const Icon(Icons.add),
                label: const Text('Ajouter un produit'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        children: [
          // Search field
          TextField(
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: 'Rechercher un produit...',
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue[600]!),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Category filter
          Row(
            children: [
              Icon(Icons.filter_list, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: DropdownButton<Category?>(
                    value: _selectedCategory,
                    hint: const Text('Toutes les catégories'),
                    isExpanded: true,
                    underline: const SizedBox(),
                    items: [
                      const DropdownMenuItem<Category?>(
                        value: null,
                        child: Text('Toutes les catégories'),
                      ),
                      ..._categories.map((Category category) {
                        return DropdownMenuItem<Category?>(
                          value: category,
                          child: Text(category.name),
                        );
                      }),
                    ],
                    onChanged: _onCategoryChanged,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductListView() {
    return ListView.builder(
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        final category = _categories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => Category(id: '', name: 'Non catégorisé'),
        );

        // Déterminer la couleur basée sur le stock
        Color stockColor;
        if (product.quantity > 5) {
          stockColor = Colors.green;
        } else if (product.quantity == 5) {
          stockColor = Colors.orange;
        } else {
          stockColor = Colors.red;
        }

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  stockColor.withValues(alpha: 0.1),
                  stockColor.withValues(alpha: 0.05),
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: stockColor.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              leading: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: stockColor.withValues(alpha: 0.1),
                  border: Border.all(
                    color: stockColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child:
                      product.imageUrl != null && product.imageUrl!.isNotEmpty
                          ? PerformanceOptimizer.optimizedNetworkImage(
                            product.imageUrl!,
                            width: 56,
                            height: 56,
                            fit: BoxFit.cover,
                            placeholder: Container(
                              width: 56,
                              height: 56,
                              color: stockColor.withValues(alpha: 0.1),
                              child: Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    stockColor,
                                  ),
                                ),
                              ),
                            ),
                            errorWidget: Icon(
                              Icons.image_not_supported,
                              size: 30,
                              color: stockColor,
                            ),
                          )
                          : Icon(
                            Icons.inventory_2_outlined,
                            size: 30,
                            color: stockColor,
                          ),
                ),
              ),
              title: Text(
                product.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    'Prix: ${product.price} € - Quantité: ${product.quantity}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: stockColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: stockColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: stockColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              trailing: PopupMenuButton<String>(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: stockColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.more_vert, color: stockColor),
                ),
                onSelected: (value) {
                  if (value == 'edit') {
                    _navigateToEditProduct(product);
                  } else if (value == 'delete') {
                    _deleteProduct(product);
                  }
                },
                itemBuilder:
                    (BuildContext context) => <PopupMenuEntry<String>>[
                      const PopupMenuItem<String>(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit, color: Colors.blue),
                          title: Text('Modifier'),
                        ),
                      ),
                      const PopupMenuItem<String>(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text(
                            'Supprimer',
                            style: TextStyle(color: Colors.red),
                          ),
                        ),
                      ),
                    ],
              ),
              onTap: () => _navigateToEditProduct(product),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProductGridView() {
    return AdaptiveGridViewBuilder(
      itemCount: _filteredProducts.length,
      forcedCrossAxisCount: 2, // Force 2 colonnes pour une expérience identique
      childAspectRatio: 0.75, // Ajusté pour accommoder la catégorie
      padding: const EdgeInsets.all(8.0),
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        final category = _categories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => Category(id: '', name: 'Non catégorisé'),
        );

        // Déterminer la couleur basée sur le stock
        Color stockColor;
        if (product.quantity > 5) {
          stockColor = Colors.green;
        } else if (product.quantity == 5) {
          stockColor = Colors.orange;
        } else {
          stockColor = Colors.red;
        }

        return Card(
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  stockColor.withValues(alpha: 0.1),
                  stockColor.withValues(alpha: 0.05),
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: stockColor.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => _navigateToEditProduct(product),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Image section avec style moderne
                  Expanded(
                    flex: 3,
                    child: Container(
                      margin: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: stockColor.withValues(alpha: 0.1),
                        border: Border.all(
                          color: stockColor.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(13),
                        child:
                            product.imageUrl != null &&
                                    product.imageUrl!.isNotEmpty
                                ? PerformanceOptimizer.optimizedNetworkImage(
                                  product.imageUrl!,
                                  fit: BoxFit.cover,
                                  placeholder: Container(
                                    color: stockColor.withValues(alpha: 0.1),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              stockColor,
                                            ),
                                      ),
                                    ),
                                  ),
                                  errorWidget: Icon(
                                    Icons.image_not_supported,
                                    size: 40,
                                    color: stockColor,
                                  ),
                                )
                                : Icon(
                                  Icons.inventory_2_outlined,
                                  size: 40,
                                  color: stockColor,
                                ),
                      ),
                    ),
                  ),
                  // Contenu avec style moderne
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Nom du produit
                          Text(
                            product.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          // Prix et stock
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Text(
                                  '${product.price} €',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.green[700],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: stockColor.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '${product.quantity}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: stockColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          // Catégorie
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: stockColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: stockColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              category.name,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: stockColor,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Helper pour afficher le nom de la catégorie (si nécessaire ailleurs)
// Future<String> _getCategoryName(String categoryId, InventoryService service) async {
//   final category = await service.getCategoryById(categoryId);
//   return category?.name ?? 'N/A';
// }
