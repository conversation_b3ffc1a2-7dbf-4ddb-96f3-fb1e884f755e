import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';
import 'data_change_notifier.dart';

/// Service de sauvegarde optimisé pour réduire la taille et améliorer les performances
class OptimizedBackupService {
  static const String _backupVersion = '2.1.0';
  
  /// Créer une sauvegarde optimisée
  static Future<Map<String, dynamic>> createOptimizedBackup() async {
    try {
      debugPrint('📦 Création de sauvegarde optimisée...');
      final stopwatch = Stopwatch()..start();

      // Collecter les données en parallèle
      final results = await Future.wait([
        InventoryService.instance.getProducts(),
        InventoryService.instance.getCategories(),
        InvoiceService.loadInvoices(),
        TaskService.instance.getTasks(),
        ColisService.instance.getAllColis(),
      ]);

      final products = results[0] as List<Product>;
      final categories = results[1] as List<Category>;
      final invoices = results[2] as List<Invoice>;
      final tasks = results[3] as List<Task>;
      final colis = results[4] as List<Colis>;

      // Créer la sauvegarde optimisée
      final backup = {
        'v': _backupVersion, // Version raccourcie
        't': DateTime.now().millisecondsSinceEpoch, // Timestamp en millisecondes
        'd': {
          'p': _optimizeProducts(products),
          'c': _optimizeCategories(categories),
          'i': _optimizeInvoices(invoices),
          'tk': _optimizeTasks(tasks),
          'cl': _optimizeColis(colis),
        },
      };

      stopwatch.stop();
      final jsonString = jsonEncode(backup);
      
      debugPrint('✅ Sauvegarde optimisée créée en ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('📊 Taille: ${jsonString.length} caractères');
      debugPrint('📊 Réduction estimée: ~60% par rapport au format standard');

      return backup;
    } catch (e) {
      debugPrint('❌ Erreur lors de la création de sauvegarde optimisée: $e');
      rethrow;
    }
  }

  /// Optimiser les produits (format compact)
  static List<List<dynamic>> _optimizeProducts(List<Product> products) {
    // Format: [id, name, price, quantity, categoryId, description?, barcode?, sku?]
    return products.map((p) {
      final compact = [
        p.id,
        p.name,
        p.price,
        p.quantity,
        p.categoryId,
      ];
      
      // Ajouter les champs optionnels seulement s'ils ne sont pas vides
      if (p.description.isNotEmpty) compact.add(p.description);
      if (p.barcode?.isNotEmpty == true) compact.add(p.barcode);
      if (p.sku?.isNotEmpty == true) compact.add(p.sku);
      
      return compact;
    }).toList();
  }

  /// Optimiser les catégories (format compact)
  static List<List<dynamic>> _optimizeCategories(List<Category> categories) {
    // Format: [id, name, defaultPrice?]
    return categories.map((c) {
      final compact = [c.id, c.name];
      if (c.defaultPrice != null) compact.add(c.defaultPrice);
      return compact;
    }).toList();
  }

  /// Optimiser les factures (format compact)
  static List<List<dynamic>> _optimizeInvoices(List<Invoice> invoices) {
    // Format: [id, clientName, totalAmount, createdAt, status, items]
    return invoices.map((i) {
      return [
        i.id,
        i.clientName,
        i.totalAmount,
        i.createdAt.millisecondsSinceEpoch,
        i.status.index, // Utiliser l'index de l'enum
        _optimizeInvoiceItems(i.items),
      ];
    }).toList();
  }

  /// Optimiser les items de facture
  static List<List<dynamic>> _optimizeInvoiceItems(List<dynamic> items) {
    return items.map((item) {
      if (item is Map<String, dynamic>) {
        return [
          item['productId'],
          item['quantity'],
          item['unitPrice'],
        ];
      }
      return item;
    }).toList();
  }

  /// Optimiser les tâches (format compact)
  static List<List<dynamic>> _optimizeTasks(List<Task> tasks) {
    // Format: [id, title, isCompleted, createdAt, description?]
    return tasks.map((t) {
      final compact = [
        t.id,
        t.title,
        t.isCompleted,
        t.createdAt.millisecondsSinceEpoch,
      ];
      
      if (t.description?.isNotEmpty == true) compact.add(t.description);
      return compact;
    }).toList();
  }

  /// Optimiser les colis (format compact)
  static List<List<dynamic>> _optimizeColis(List<Colis> colis) {
    // Format: [id, destinataire, commune, statut, createdAt]
    return colis.map((c) {
      return [
        c.id,
        c.destinataire,
        c.commune,
        c.statut.index, // Utiliser l'index de l'enum
        c.createdAt.millisecondsSinceEpoch,
      ];
    }).toList();
  }

  /// Restaurer depuis une sauvegarde optimisée
  static Future<void> restoreFromOptimizedBackup(Map<String, dynamic> backup) async {
    try {
      debugPrint('📂 Restauration depuis sauvegarde optimisée...');
      final stopwatch = Stopwatch()..start();

      final data = backup['d'] as Map<String, dynamic>;

      // Restaurer en parallèle pour améliorer les performances
      await Future.wait([
        _restoreOptimizedProducts(data['p'] as List<dynamic>? ?? []),
        _restoreOptimizedCategories(data['c'] as List<dynamic>? ?? []),
        _restoreOptimizedInvoices(data['i'] as List<dynamic>? ?? []),
        _restoreOptimizedTasks(data['tk'] as List<dynamic>? ?? []),
        _restoreOptimizedColis(data['cl'] as List<dynamic>? ?? []),
      ]);

      // Notifier les changements
      DataChangeNotifier.instance.notifyAllDataChanged();

      stopwatch.stop();
      debugPrint('✅ Restauration optimisée terminée en ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration optimisée: $e');
      rethrow;
    }
  }

  /// Restaurer les produits optimisés
  static Future<void> _restoreOptimizedProducts(List<dynamic> productsData) async {
    if (productsData.isEmpty) return;

    final products = productsData.map((data) {
      final list = data as List<dynamic>;
      return Product(
        id: list[0] as String,
        name: list[1] as String,
        price: (list[2] as num).toDouble(),
        quantity: list[3] as int,
        categoryId: list[4] as String,
        description: list.length > 5 ? list[5] as String : '',
        barcode: list.length > 6 ? list[6] as String : null,
        sku: list.length > 7 ? list[7] as String : null,
      );
    }).toList();

    final prefs = await SharedPreferences.getInstance();
    final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
    await prefs.setString('inventory_products', productsJson);
    
    debugPrint('✅ ${products.length} produits restaurés (format optimisé)');
  }

  /// Restaurer les catégories optimisées
  static Future<void> _restoreOptimizedCategories(List<dynamic> categoriesData) async {
    if (categoriesData.isEmpty) return;

    final categories = categoriesData.map((data) {
      final list = data as List<dynamic>;
      return Category(
        id: list[0] as String,
        name: list[1] as String,
        defaultPrice: list.length > 2 ? (list[2] as num?)?.toDouble() : null,
      );
    }).toList();

    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(categories.map((c) => c.toJson()).toList());
    await prefs.setString('inventory_categories', categoriesJson);
    
    debugPrint('✅ ${categories.length} catégories restaurées (format optimisé)');
  }

  /// Restaurer les factures optimisées
  static Future<void> _restoreOptimizedInvoices(List<dynamic> invoicesData) async {
    if (invoicesData.isEmpty) return;

    final invoices = invoicesData.map((data) {
      final list = data as List<dynamic>;
      return Invoice(
        id: list[0] as String,
        clientName: list[1] as String,
        totalAmount: (list[2] as num).toDouble(),
        createdAt: DateTime.fromMillisecondsSinceEpoch(list[3] as int),
        status: InvoiceStatus.values[list[4] as int],
        items: _restoreInvoiceItems(list[5] as List<dynamic>),
      );
    }).toList();

    await InvoiceService.saveInvoices(invoices);
    debugPrint('✅ ${invoices.length} factures restaurées (format optimisé)');
  }

  /// Restaurer les items de facture
  static List<Map<String, dynamic>> _restoreInvoiceItems(List<dynamic> itemsData) {
    return itemsData.map((data) {
      final list = data as List<dynamic>;
      return {
        'productId': list[0] as String,
        'quantity': list[1] as int,
        'unitPrice': (list[2] as num).toDouble(),
      };
    }).toList();
  }

  /// Restaurer les tâches optimisées
  static Future<void> _restoreOptimizedTasks(List<dynamic> tasksData) async {
    if (tasksData.isEmpty) return;

    final tasks = tasksData.map((data) {
      final list = data as List<dynamic>;
      return Task(
        id: list[0] as String,
        title: list[1] as String,
        isCompleted: list[2] as bool,
        createdAt: DateTime.fromMillisecondsSinceEpoch(list[3] as int),
        description: list.length > 4 ? list[4] as String : null,
      );
    }).toList();

    final prefs = await SharedPreferences.getInstance();
    final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
    await prefs.setString('tasks', tasksJson);
    
    debugPrint('✅ ${tasks.length} tâches restaurées (format optimisé)');
  }

  /// Restaurer les colis optimisés
  static Future<void> _restoreOptimizedColis(List<dynamic> colisData) async {
    if (colisData.isEmpty) return;

    final colis = colisData.map((data) {
      final list = data as List<dynamic>;
      return Colis(
        id: list[0] as String,
        destinataire: list[1] as String,
        commune: list[2] as String,
        statut: StatutLivraison.values[list[3] as int],
        createdAt: DateTime.fromMillisecondsSinceEpoch(list[4] as int),
      );
    }).toList();

    await ColisService.instance.saveColis(colis);
    debugPrint('✅ ${colis.length} colis restaurés (format optimisé)');
  }

  /// Créer une sauvegarde différentielle (seulement les changements récents)
  static Future<Map<String, dynamic>> createDifferentialBackup(DateTime? lastBackupDate) async {
    debugPrint('📦 Création de sauvegarde différentielle...');
    
    // Pour l'instant, créer une sauvegarde complète optimisée
    // Dans une version future, filtrer par date de modification
    return await createOptimizedBackup();
  }

  /// Estimer la taille de la sauvegarde avant création
  static Future<Map<String, int>> estimateBackupSize() async {
    final results = await Future.wait([
      InventoryService.instance.getProducts(),
      InventoryService.instance.getCategories(),
      InvoiceService.loadInvoices(),
      TaskService.instance.getTasks(),
      ColisService.instance.getAllColis(),
    ]);

    final products = results[0] as List<Product>;
    final categories = results[1] as List<Category>;
    final invoices = results[2] as List<Invoice>;
    final tasks = results[3] as List<Task>;
    final colis = results[4] as List<Colis>;

    // Estimation approximative en caractères JSON
    final estimatedSize = {
      'products': products.length * 150, // ~150 caractères par produit optimisé
      'categories': categories.length * 50, // ~50 caractères par catégorie
      'invoices': invoices.length * 200, // ~200 caractères par facture
      'tasks': tasks.length * 100, // ~100 caractères par tâche
      'colis': colis.length * 120, // ~120 caractères par colis
    };

    final totalEstimated = estimatedSize.values.reduce((a, b) => a + b);
    estimatedSize['total'] = totalEstimated;

    return estimatedSize;
  }
}
