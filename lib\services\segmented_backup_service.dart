import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart' as model;
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';

/// Types de données disponibles pour la sauvegarde
enum BackupDataType {
  products('Produits', 'products'),
  categories('Catégories', 'categories'),
  invoices('Factures', 'invoices'),
  tasks('Tâches', 'tasks'),
  colis('Colis', 'colis');

  const BackupDataType(this.displayName, this.key);
  final String displayName;
  final String key;
}

/// Période de sauvegarde
class BackupPeriod {
  final DateTime startDate;
  final DateTime endDate;
  final String displayName;

  BackupPeriod({
    required this.startDate,
    required this.endDate,
    required this.displayName,
  });

  /// Créer une période pour un mois spécifique
  factory BackupPeriod.month(int year, int month) {
    final startDate = DateTime(year, month, 1);
    final endDate = DateTime(year, month + 1, 0, 23, 59, 59);
    final monthNames = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];

    return BackupPeriod(
      startDate: startDate,
      endDate: endDate,
      displayName: '${monthNames[month - 1]} $year',
    );
  }

  /// Créer une période pour une année complète
  factory BackupPeriod.year(int year) {
    return BackupPeriod(
      startDate: DateTime(year, 1, 1),
      endDate: DateTime(year, 12, 31, 23, 59, 59),
      displayName: 'Année $year',
    );
  }

  /// Créer une période personnalisée
  factory BackupPeriod.custom(DateTime start, DateTime end, String name) {
    return BackupPeriod(startDate: start, endDate: end, displayName: name);
  }

  /// Toutes les données (sans filtre de date)
  factory BackupPeriod.all() {
    return BackupPeriod(
      startDate: DateTime(2020, 1, 1),
      endDate: DateTime.now().add(const Duration(days: 365)),
      displayName: 'Toutes les données',
    );
  }
}

/// Configuration de sauvegarde segmentée
class SegmentedBackupConfig {
  final List<BackupDataType> dataTypes;
  final BackupPeriod? period;
  final bool includeImages;
  final bool compressData;

  SegmentedBackupConfig({
    required this.dataTypes,
    this.period,
    this.includeImages = false,
    this.compressData = true,
  });
}

/// Service de sauvegarde segmentée
class SegmentedBackupService {
  static const String _backupVersion = '2.2.0';

  /// Créer une sauvegarde segmentée
  static Future<Map<String, dynamic>> createSegmentedBackup(
    SegmentedBackupConfig config, {
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      debugPrint('📦 Création de sauvegarde segmentée...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Initialisation de la sauvegarde segmentée...');

      final backup = {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'segmented': true,
        'config': {
          'dataTypes': config.dataTypes.map((e) => e.key).toList(),
          'period':
              config.period != null
                  ? {
                    'startDate': config.period!.startDate.toIso8601String(),
                    'endDate': config.period!.endDate.toIso8601String(),
                    'displayName': config.period!.displayName,
                  }
                  : null,
          'includeImages': config.includeImages,
          'compressData': config.compressData,
        },
        'data': <String, dynamic>{},
        'stats': <String, dynamic>{},
      };

      double progressStep = 1.0 / config.dataTypes.length;
      double currentProgress = 0.1;

      // Traiter chaque type de données sélectionné
      for (int i = 0; i < config.dataTypes.length; i++) {
        final dataType = config.dataTypes[i];

        onProgress?.call(
          currentProgress,
          'Traitement: ${dataType.displayName}...',
        );

        final data = await _processDataType(dataType, config);
        (backup['data'] as Map<String, dynamic>)[dataType.key] = data['items'];
        (backup['stats'] as Map<String, dynamic>)[dataType.key] = data['stats'];

        currentProgress += progressStep;
      }

      onProgress?.call(0.9, 'Finalisation...');

      // Ajouter les métadonnées globales
      backup['globalStats'] = _calculateGlobalStats(
        backup['stats'] as Map<String, dynamic>,
      );

      stopwatch.stop();
      onProgress?.call(1.0, 'Sauvegarde segmentée terminée');

      final jsonString = jsonEncode(backup);
      debugPrint(
        '✅ Sauvegarde segmentée créée en ${stopwatch.elapsedMilliseconds}ms',
      );
      debugPrint('📊 Taille: ${jsonString.length} caractères');

      return backup;
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde segmentée: $e');
      rethrow;
    }
  }

  /// Traiter un type de données spécifique
  static Future<Map<String, dynamic>> _processDataType(
    BackupDataType dataType,
    SegmentedBackupConfig config,
  ) async {
    switch (dataType) {
      case BackupDataType.products:
        return await _processProducts(config);
      case BackupDataType.categories:
        return await _processCategories(config);
      case BackupDataType.invoices:
        return await _processInvoices(config);
      case BackupDataType.tasks:
        return await _processTasks(config);
      case BackupDataType.colis:
        return await _processColis(config);
    }
  }

  /// Traiter les produits
  static Future<Map<String, dynamic>> _processProducts(
    SegmentedBackupConfig config,
  ) async {
    final allProducts = await InventoryService.instance.getProducts();

    // Les produits n'ont généralement pas de date de création dans le modèle actuel
    // On prend tous les produits pour l'instant
    final filteredProducts = allProducts;

    final optimizedProducts =
        filteredProducts.map((product) {
          final json = product.toJson();
          if (!config.includeImages) {
            json.remove('image');
            json.remove('imageBase64');
          }
          json.removeWhere(
            (key, value) => value == null || (value is String && value.isEmpty),
          );
          return json;
        }).toList();

    return {
      'items': optimizedProducts,
      'stats': {
        'total': filteredProducts.length,
        'totalValue': filteredProducts.fold<double>(
          0,
          (sum, p) => sum + (p.price * p.quantity),
        ),
      },
    };
  }

  /// Traiter les catégories
  static Future<Map<String, dynamic>> _processCategories(
    SegmentedBackupConfig config,
  ) async {
    final allCategories = await InventoryService.instance.getCategories();

    final optimizedCategories =
        allCategories.map((category) {
          final json = category.toJson();
          json.removeWhere(
            (key, value) => value == null || (value is String && value.isEmpty),
          );
          return json;
        }).toList();

    return {
      'items': optimizedCategories,
      'stats': {'total': allCategories.length},
    };
  }

  /// Traiter les factures avec filtre de date
  static Future<Map<String, dynamic>> _processInvoices(
    SegmentedBackupConfig config,
  ) async {
    final allInvoices = await InvoiceService.loadInvoices();

    // Filtrer par période si spécifiée
    List<Invoice> filteredInvoices = allInvoices;
    if (config.period != null) {
      filteredInvoices =
          allInvoices.where((invoice) {
            return invoice.createdAt.isAfter(config.period!.startDate) &&
                invoice.createdAt.isBefore(config.period!.endDate);
          }).toList();
    }

    final optimizedInvoices =
        filteredInvoices.map((invoice) {
          final json = invoice.toJson();
          if (!config.includeImages) {
            json.remove('imageBase64');
            json.remove('signatureBase64');
          }
          json.removeWhere(
            (key, value) =>
                value == null ||
                (value is String && value.isEmpty) ||
                (value is List && value.isEmpty),
          );
          return json;
        }).toList();

    return {
      'items': optimizedInvoices,
      'stats': {
        'total': filteredInvoices.length,
        'totalAmount': filteredInvoices.fold<double>(
          0,
          (sum, i) => sum + i.total,
        ),
        'period': config.period?.displayName ?? 'Toutes',
      },
    };
  }

  /// Traiter les tâches avec filtre de date
  static Future<Map<String, dynamic>> _processTasks(
    SegmentedBackupConfig config,
  ) async {
    final allTasks = await TaskService.instance.getTasks();

    // Filtrer par période si spécifiée
    List<Task> filteredTasks = allTasks;
    if (config.period != null) {
      filteredTasks =
          allTasks.where((task) {
            return task.createdAt.isAfter(config.period!.startDate) &&
                task.createdAt.isBefore(config.period!.endDate);
          }).toList();
    }

    final optimizedTasks =
        filteredTasks.map((task) {
          final json = task.toJson();
          json.removeWhere(
            (key, value) => value == null || (value is String && value.isEmpty),
          );
          return json;
        }).toList();

    return {
      'items': optimizedTasks,
      'stats': {
        'total': filteredTasks.length,
        'completed': filteredTasks.where((t) => t.isCompleted).length,
        'pending': filteredTasks.where((t) => !t.isCompleted).length,
        'period': config.period?.displayName ?? 'Toutes',
      },
    };
  }

  /// Traiter les colis avec filtre de date
  static Future<Map<String, dynamic>> _processColis(
    SegmentedBackupConfig config,
  ) async {
    final allColis = await ColisService.instance.getAllColis();

    // Filtrer par période si spécifiée
    List<Colis> filteredColis = allColis;
    if (config.period != null) {
      filteredColis =
          allColis.where((colis) {
            return colis.dateAjout.isAfter(config.period!.startDate) &&
                colis.dateAjout.isBefore(config.period!.endDate);
          }).toList();
    }

    final optimizedColis =
        filteredColis.map((colis) {
          final json = colis.toJson();
          json.removeWhere(
            (key, value) => value == null || (value is String && value.isEmpty),
          );
          return json;
        }).toList();

    return {
      'items': optimizedColis,
      'stats': {
        'total': filteredColis.length,
        'delivered':
            filteredColis
                .where((c) => c.statut == StatutLivraison.livree)
                .length,
        'pending':
            filteredColis
                .where((c) => c.statut == StatutLivraison.enCours)
                .length,
        'period': config.period?.displayName ?? 'Toutes',
      },
    };
  }

  /// Calculer les statistiques globales
  static Map<String, dynamic> _calculateGlobalStats(
    Map<String, dynamic> stats,
  ) {
    int totalItems = 0;
    double totalValue = 0;

    stats.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        totalItems += (value['total'] as int? ?? 0);
        totalValue += (value['totalAmount'] as double? ?? 0);
        totalValue += (value['totalValue'] as double? ?? 0);
      }
    });

    return {
      'totalItems': totalItems,
      'totalValue': totalValue,
      'dataTypes': stats.keys.length,
    };
  }

  /// Obtenir les mois disponibles pour un type de données
  static Future<List<BackupPeriod>> getAvailableMonths(
    BackupDataType dataType,
  ) async {
    final periods = <BackupPeriod>[];
    final now = DateTime.now();

    // Ajouter "Toutes les données"
    periods.add(BackupPeriod.all());

    // Ajouter les 12 derniers mois
    for (int i = 0; i < 12; i++) {
      final date = DateTime(now.year, now.month - i, 1);
      if (date.year >= 2020) {
        // Limite raisonnable
        periods.add(BackupPeriod.month(date.year, date.month));
      }
    }

    return periods;
  }

  /// Estimer la taille d'une sauvegarde segmentée
  static Future<Map<String, dynamic>> estimateSegmentedBackupSize(
    SegmentedBackupConfig config,
  ) async {
    final estimates = <String, int>{};
    int totalSize = 0;

    for (final dataType in config.dataTypes) {
      int estimatedSize = 0;

      switch (dataType) {
        case BackupDataType.products:
          final products = await InventoryService.instance.getProducts();
          estimatedSize = products.length * (config.includeImages ? 500 : 200);
          break;
        case BackupDataType.categories:
          final categories = await InventoryService.instance.getCategories();
          estimatedSize = categories.length * 80;
          break;
        case BackupDataType.invoices:
          final invoices = await InvoiceService.loadInvoices();
          estimatedSize = invoices.length * (config.includeImages ? 1000 : 400);
          break;
        case BackupDataType.tasks:
          final tasks = await TaskService.instance.getTasks();
          estimatedSize = tasks.length * 150;
          break;
        case BackupDataType.colis:
          final colis = await ColisService.instance.getAllColis();
          estimatedSize = colis.length * 200;
          break;
      }

      estimates[dataType.key] = estimatedSize;
      totalSize += estimatedSize;
    }

    return {
      'estimates': estimates,
      'totalSize': totalSize,
      'totalSizeKB': (totalSize / 1024).round(),
    };
  }

  /// Restaurer depuis une sauvegarde segmentée
  static Future<void> restoreFromSegmentedBackup(
    Map<String, dynamic> backup, {
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      debugPrint('📂 Restauration depuis sauvegarde segmentée...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Vérification de la sauvegarde segmentée...');

      // Vérifier que c'est bien une sauvegarde segmentée
      if (backup['segmented'] != true) {
        throw Exception('Ce n\'est pas une sauvegarde segmentée valide');
      }

      final data = backup['data'] as Map<String, dynamic>;
      final config = backup['config'] as Map<String, dynamic>;
      final dataTypes = (config['dataTypes'] as List<dynamic>).cast<String>();

      double progressStep = 0.8 / dataTypes.length;
      double currentProgress = 0.1;

      // Restaurer chaque type de données
      for (final dataTypeKey in dataTypes) {
        final dataType = BackupDataType.values.firstWhere(
          (dt) => dt.key == dataTypeKey,
        );

        onProgress?.call(
          currentProgress,
          'Restauration: ${dataType.displayName}...',
        );

        final items = data[dataTypeKey] as List<dynamic>? ?? [];
        await _restoreDataType(dataType, items);

        currentProgress += progressStep;
      }

      onProgress?.call(0.9, 'Finalisation...');

      // Notifier les changements
      // DataChangeNotifier.instance.notifyAllDataChanged();

      stopwatch.stop();
      onProgress?.call(1.0, 'Restauration segmentée terminée');
      debugPrint(
        '✅ Restauration segmentée terminée en ${stopwatch.elapsedMilliseconds}ms',
      );
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration segmentée: $e');
      rethrow;
    }
  }

  /// Restaurer un type de données spécifique
  static Future<void> _restoreDataType(
    BackupDataType dataType,
    List<dynamic> items,
  ) async {
    switch (dataType) {
      case BackupDataType.products:
        await _restoreProducts(items);
        break;
      case BackupDataType.categories:
        await _restoreCategories(items);
        break;
      case BackupDataType.invoices:
        await _restoreInvoices(items);
        break;
      case BackupDataType.tasks:
        await _restoreTasks(items);
        break;
      case BackupDataType.colis:
        await _restoreColis(items);
        break;
    }
  }

  /// Restaurer les produits
  static Future<void> _restoreProducts(List<dynamic> productsData) async {
    if (productsData.isEmpty) return;

    final products =
        productsData
            .map((json) => Product.fromJson(json as Map<String, dynamic>))
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
    await prefs.setString('inventory_products', productsJson);

    debugPrint('✅ ${products.length} produits restaurés (segmenté)');
  }

  /// Restaurer les catégories
  static Future<void> _restoreCategories(List<dynamic> categoriesData) async {
    if (categoriesData.isEmpty) return;

    final categories =
        categoriesData
            .map(
              (json) => model.Category.fromJson(json as Map<String, dynamic>),
            )
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(
      categories.map((c) => c.toJson()).toList(),
    );
    await prefs.setString('inventory_categories', categoriesJson);

    debugPrint('✅ ${categories.length} catégories restaurées (segmenté)');
  }

  /// Restaurer les factures
  static Future<void> _restoreInvoices(List<dynamic> invoicesData) async {
    if (invoicesData.isEmpty) return;

    final invoices =
        invoicesData
            .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
            .toList();

    await InvoiceService.saveInvoices(invoices);
    debugPrint('✅ ${invoices.length} factures restaurées (segmenté)');
  }

  /// Restaurer les tâches
  static Future<void> _restoreTasks(List<dynamic> tasksData) async {
    if (tasksData.isEmpty) return;

    final tasks =
        tasksData
            .map((json) => Task.fromJson(json as Map<String, dynamic>))
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
    await prefs.setString('tasks', tasksJson);

    debugPrint('✅ ${tasks.length} tâches restaurées (segmenté)');
  }

  /// Restaurer les colis
  static Future<void> _restoreColis(List<dynamic> colisData) async {
    if (colisData.isEmpty) return;

    final colis =
        colisData
            .map((json) => Colis.fromJson(json as Map<String, dynamic>))
            .toList();

    await ColisService.instance.saveColis(colis);
    debugPrint('✅ ${colis.length} colis restaurés (segmenté)');
  }

  /// Vérifier si une sauvegarde est segmentée
  static bool isSegmentedBackup(Map<String, dynamic> backup) {
    return backup['segmented'] == true;
  }

  /// Obtenir les informations d'une sauvegarde segmentée
  static Map<String, dynamic> getSegmentedBackupInfo(
    Map<String, dynamic> backup,
  ) {
    if (!isSegmentedBackup(backup)) {
      throw Exception('Ce n\'est pas une sauvegarde segmentée');
    }

    final config = backup['config'] as Map<String, dynamic>;
    final stats = backup['stats'] as Map<String, dynamic>;
    final globalStats = backup['globalStats'] as Map<String, dynamic>;

    return {
      'version': backup['version'],
      'timestamp': backup['timestamp'],
      'dataTypes': config['dataTypes'],
      'period': config['period'],
      'includeImages': config['includeImages'],
      'compressData': config['compressData'],
      'stats': stats,
      'globalStats': globalStats,
    };
  }
}
