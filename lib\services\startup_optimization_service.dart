import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'data_persistence_service.dart';
import 'data_migration_service.dart';
import 'version_service.dart';
import 'performance_optimization_service.dart';

/// Service pour optimiser le démarrage de l'application
class StartupOptimizationService {
  static StartupOptimizationService? _instance;
  static StartupOptimizationService get instance {
    _instance ??= StartupOptimizationService._internal();
    return _instance!;
  }

  StartupOptimizationService._internal();

  bool _isInitialized = false;
  bool _migrationInProgress = false;
  final Completer<void> _initializationCompleter = Completer<void>();

  /// Initialiser l'application de manière optimisée
  Future<void> initializeApp() async {
    if (_isInitialized) {
      debugPrint('✅ Application déjà initialisée');
      return;
    }

    if (_migrationInProgress) {
      debugPrint('⏳ Migration en cours, attente...');
      await _initializationCompleter.future;
      return;
    }

    _migrationInProgress = true;
    debugPrint('🚀 Initialisation optimisée de l\'application...');

    try {
      final stopwatch = Stopwatch()..start();

      // 1. Vérifier si l'application a déjà été initialisée
      final isFirstLaunch = await _checkFirstLaunch();
      
      // 2. Vérifier si une migration est nécessaire
      final needsMigration = await _checkMigrationNeeded();

      if (isFirstLaunch) {
        await _performFirstLaunchSetup();
      } else if (needsMigration) {
        await _performOptimizedMigration();
      } else {
        await _performQuickStartup();
      }

      stopwatch.stop();
      debugPrint('✅ Initialisation terminée en ${stopwatch.elapsedMilliseconds}ms');

      _isInitialized = true;
      _migrationInProgress = false;
      
      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.complete();
      }

    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation: $e');
      _migrationInProgress = false;
      
      if (!_initializationCompleter.isCompleted) {
        _initializationCompleter.completeError(e);
      }
      rethrow;
    }
  }

  /// Vérifier si c'est le premier lancement
  Future<bool> _checkFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool('app_initialized') ?? false);
  }

  /// Vérifier si une migration est nécessaire
  Future<bool> _checkMigrationNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Obtenir la version actuelle
      await VersionService.instance.initialize();
      final currentVersion = VersionService.instance.version;
      
      // Vérifier la dernière version migrée
      final lastMigrationVersion = prefs.getString('migration_version');
      final migrationCompleted = prefs.getBool('migration_completed_$currentVersion') ?? false;
      
      // Migration nécessaire si :
      // - Aucune migration précédente
      // - Version différente
      // - Migration non complétée pour cette version
      return lastMigrationVersion == null || 
             lastMigrationVersion != currentVersion || 
             !migrationCompleted;
             
    } catch (e) {
      debugPrint('⚠️ Erreur lors de la vérification de migration: $e');
      return true; // En cas de doute, faire la migration
    }
  }

  /// Configuration pour le premier lancement
  Future<void> _performFirstLaunchSetup() async {
    debugPrint('🆕 Premier lancement détecté');
    
    await PerformanceOptimizationService.instance.executeInBackground(
      'FirstLaunchSetup',
      () async {
        final prefs = await SharedPreferences.getInstance();
        
        // Initialiser les paramètres par défaut
        await prefs.setBool('app_initialized', true);
        await prefs.setBool('data_persistence_enabled', true);
        await prefs.setString('first_launch_date', DateTime.now().toIso8601String());
        
        // Initialiser les structures de données
        await DataPersistenceService.instance.saveAllData(
          products: [],
          categories: [],
          invoices: [],
          tasks: [],
          colis: [],
        );
        
        // Effectuer la migration si nécessaire
        await VersionService.instance.initialize();
        final currentVersion = VersionService.instance.version;
        await DataMigrationService.instance.migrateToCurrentVersion(currentVersion);
        
        debugPrint('✅ Premier lancement configuré');
      },
    );
  }

  /// Migration optimisée
  Future<void> _performOptimizedMigration() async {
    debugPrint('🔄 Migration optimisée en cours...');
    
    await PerformanceOptimizationService.instance.executeInBackground(
      'OptimizedMigration',
      () async {
        // Effectuer la migration avec throttling pour éviter les répétitions
        await PerformanceOptimizationService.instance.throttle(
          'data_migration',
          const Duration(minutes: 1), // Éviter les migrations répétées dans la minute
          () async {
            await VersionService.instance.initialize();
            final currentVersion = VersionService.instance.version;
            await DataMigrationService.instance.migrateToCurrentVersion(currentVersion);
            
            // Marquer la migration comme terminée
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('migration_completed_$currentVersion', true);
            await prefs.setString('last_successful_migration', DateTime.now().toIso8601String());
          },
        );
        
        debugPrint('✅ Migration optimisée terminée');
      },
    );
  }

  /// Démarrage rapide (pas de migration nécessaire)
  Future<void> _performQuickStartup() async {
    debugPrint('⚡ Démarrage rapide (pas de migration nécessaire)');
    
    // Vérifier que les données sont accessibles
    final hasData = await DataPersistenceService.instance.hasData();
    
    if (!hasData) {
      debugPrint('⚠️ Aucune donnée trouvée, initialisation basique');
      await _performFirstLaunchSetup();
    } else {
      debugPrint('✅ Données existantes trouvées, démarrage rapide');
    }
  }

  /// Vérifier l'état de l'initialisation
  bool get isInitialized => _isInitialized;
  bool get migrationInProgress => _migrationInProgress;

  /// Attendre que l'initialisation soit terminée
  Future<void> waitForInitialization() async {
    if (_isInitialized) return;
    await _initializationCompleter.future;
  }

  /// Forcer une réinitialisation (pour les tests ou le debug)
  Future<void> forceReset() async {
    debugPrint('🔄 Réinitialisation forcée...');
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('app_initialized');
    await prefs.remove('migration_version');
    await prefs.remove('data_persistence_enabled');
    
    // Effacer toutes les clés de migration
    final keys = prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith('migration_completed_')) {
        await prefs.remove(key);
      }
    }
    
    _isInitialized = false;
    _migrationInProgress = false;
    
    debugPrint('✅ Réinitialisation terminée');
  }

  /// Obtenir les statistiques de démarrage
  Future<Map<String, dynamic>> getStartupStats() async {
    final prefs = await SharedPreferences.getInstance();
    
    return {
      'isInitialized': _isInitialized,
      'migrationInProgress': _migrationInProgress,
      'appInitialized': prefs.getBool('app_initialized') ?? false,
      'firstLaunchDate': prefs.getString('first_launch_date'),
      'lastSuccessfulMigration': prefs.getString('last_successful_migration'),
      'dataPersistenceEnabled': prefs.getBool('data_persistence_enabled') ?? false,
    };
  }

  /// Diagnostiquer les problèmes de démarrage
  Future<List<String>> diagnoseStartupIssues() async {
    final issues = <String>[];
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Vérifier les indicateurs de base
      if (!(prefs.getBool('app_initialized') ?? false)) {
        issues.add('Application non initialisée');
      }
      
      if (!(prefs.getBool('data_persistence_enabled') ?? false)) {
        issues.add('Persistance des données désactivée');
      }
      
      // Vérifier les données
      final hasData = await DataPersistenceService.instance.hasData();
      if (!hasData) {
        issues.add('Aucune donnée persistante trouvée');
      }
      
      // Vérifier les migrations
      await VersionService.instance.initialize();
      final currentVersion = VersionService.instance.version;
      final migrationCompleted = prefs.getBool('migration_completed_$currentVersion') ?? false;
      
      if (!migrationCompleted) {
        issues.add('Migration non complétée pour la version $currentVersion');
      }
      
    } catch (e) {
      issues.add('Erreur lors du diagnostic: $e');
    }
    
    return issues;
  }
}
