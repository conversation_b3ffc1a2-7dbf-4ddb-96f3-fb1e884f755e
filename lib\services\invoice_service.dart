import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';
import 'gamification_service.dart';
import 'firebase_service.dart';
import 'mobile_optimization_service.dart';
import 'data_change_notifier.dart';
import 'inventory_service.dart';

class InvoiceService {
  static const String _invoicesKey = 'hcp_invoices';
  final Uuid _uuid = const Uuid();

  // Sauvegarder toutes les factures avec optimisation mobile
  static Future<void> saveInvoices(List<Invoice> invoices) async {
    try {
      debugPrint('🔄 Début sauvegarde ${invoices.length} factures...');

      final prefs = await SharedPreferences.getInstance();

      // Optimisation mobile : traitement par petits lots
      if (invoices.length > 50) {
        debugPrint('📱 Mode mobile : traitement par lots');
        await _saveLargeInvoiceList(prefs, invoices);
      } else {
        // Sauvegarde normale pour petites listes
        final invoicesJson = jsonEncode(
          invoices.map((inv) => inv.toJson()).toList(),
        );
        await prefs.setString(_invoicesKey, invoicesJson);
      }

      debugPrint('✅ ${invoices.length} factures sauvegardées');
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des factures: $e');
      rethrow;
    }
  }

  // Sauvegarde optimisée pour grandes listes (mobile)
  static Future<void> _saveLargeInvoiceList(
    SharedPreferences prefs,
    List<Invoice> invoices,
  ) async {
    const batchSize = 25; // Traiter par lots de 25
    final batches = <List<Invoice>>[];

    // Diviser en lots
    for (int i = 0; i < invoices.length; i += batchSize) {
      final end =
          (i + batchSize < invoices.length) ? i + batchSize : invoices.length;
      batches.add(invoices.sublist(i, end));
    }

    // Traiter chaque lot avec une pause pour éviter le blocage
    final allJsonData = <Map<String, dynamic>>[];
    for (int i = 0; i < batches.length; i++) {
      final batch = batches[i];
      debugPrint(
        '📦 Traitement lot ${i + 1}/${batches.length} (${batch.length} factures)',
      );

      // Convertir le lot en JSON
      final batchJson = batch.map((inv) => inv.toJson()).toList();
      allJsonData.addAll(batchJson);

      // Pause pour éviter le blocage de l'UI (important sur mobile)
      if (i < batches.length - 1) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
    }

    // Sauvegarder tout d'un coup
    final finalJson = jsonEncode(allJsonData);
    await prefs.setString(_invoicesKey, finalJson);
  }

  // Charger toutes les factures - Version simplifiée et fiable
  static Future<List<Invoice>> loadInvoices() async {
    try {
      debugPrint('🔄 Chargement des factures...');

      // Charger directement depuis SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_invoicesKey);

      if (jsonString == null || jsonString.isEmpty) {
        debugPrint('📭 Aucune facture trouvée en local');
        return [];
      }

      try {
        final jsonList = jsonDecode(jsonString) as List;
        final invoices =
            jsonList.map((json) => Invoice.fromJson(json)).toList();
        debugPrint(
          '✅ ${invoices.length} factures chargées depuis le stockage local',
        );
        return invoices;
      } catch (e) {
        debugPrint('❌ Erreur parsing factures: $e');
        // En cas d'erreur de parsing, nettoyer les données corrompues
        await prefs.remove(_invoicesKey);
        return [];
      }
    } catch (e) {
      debugPrint('❌ Erreur critique lors du chargement: $e');
      return [];
    }
  }

  // Charger les factures directement depuis SharedPreferences sans migration
  static Future<List<Invoice>> loadInvoicesLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_invoicesKey);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        return jsonList.map((json) => Invoice.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      print('Error loading invoices locally: $e');
      return [];
    }
  }

  // Ajouter une nouvelle facture avec optimisation mobile intelligente
  Future<Invoice> addInvoice(Invoice invoice) async {
    debugPrint('🔄 Début ajout facture...');
    final stopwatch = Stopwatch()..start();

    final newInvoice = invoice.copyWith(id: _uuid.v4());
    final mobileOptimization = MobileOptimizationService.instance;

    try {
      // Vérifier si on doit utiliser le mode optimisé
      final shouldOptimize = await mobileOptimization.shouldUseOptimizedMode();

      if (shouldOptimize) {
        debugPrint('📱 Mode mobile optimisé activé - Sauvegarde ultra-rapide');

        // Sauvegarde locale immédiate et optimisée
        await _saveInvoiceLocallyOptimized(newInvoice);

        // Notifier immédiatement pour mise à jour UI
        DataChangeNotifier.instance.notifyInvoicesChanged();
        debugPrint(
          '✅ Facture sauvegardée localement en ${stopwatch.elapsedMilliseconds}ms',
        );

        // Firebase en arrière-plan (complètement non bloquant)
        Future.microtask(() => _saveToFirebaseInBackground(newInvoice));

        // Nettoyage mémoire sur mobile si nécessaire
        mobileOptimization.forceGarbageCollection();
      } else {
        debugPrint('💻 Mode desktop standard');
        // Mode desktop : sauvegarde normale
        await _saveInvoiceLocally(newInvoice);

        try {
          await FirebaseService.instance.addInvoice(newInvoice);
        } catch (e) {
          debugPrint('Firebase save failed: $e');
        }

        // Notifier après sauvegarde sur desktop
        DataChangeNotifier.instance.notifyInvoicesChanged();
      }

      stopwatch.stop();
      debugPrint('✅ Facture ajoutée en ${stopwatch.elapsedMilliseconds}ms');

      // Notification déjà envoyée selon le mode
      try {
        debugPrint('📢 Notification de changement envoyée');
      } catch (e) {
        debugPrint('⚠️ Erreur notification: $e');
      }

      return newInvoice;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '❌ Erreur ajout facture: $e (${stopwatch.elapsedMilliseconds}ms)',
      );
      rethrow;
    }
  }

  // Sauvegarde Firebase en arrière-plan (non bloquante)
  void _saveToFirebaseInBackground(Invoice invoice) {
    Future.microtask(() async {
      try {
        debugPrint('🔄 Sauvegarde Firebase en arrière-plan...');
        await FirebaseService.instance.addInvoice(invoice);
        debugPrint('✅ Sauvegarde Firebase réussie');
      } catch (e) {
        debugPrint('⚠️ Sauvegarde Firebase échouée (non critique): $e');
      }
    });
  }

  // Sauvegarde optimisée pour mobile - Version simplifiée et fiable
  Future<void> _saveInvoiceLocallyOptimized(Invoice invoice) async {
    try {
      debugPrint('🔄 Sauvegarde locale optimisée...');

      // Vérifier que l'ID de la facture n'est pas null ou vide
      if (invoice.id.isEmpty) {
        debugPrint('❌ Erreur: ID de facture vide');
        throw Exception('ID de facture invalide');
      }

      // Charger toutes les factures existantes
      final existingInvoices = await loadInvoices();

      // Vérifier si la facture existe déjà (éviter les doublons)
      int existingIndex = -1;
      try {
        existingIndex = existingInvoices.indexWhere(
          (inv) => inv.id == invoice.id,
        );
      } catch (e) {
        debugPrint('⚠️ Erreur lors de la recherche de facture: $e');
        existingIndex = -1;
      }

      if (existingIndex >= 0) {
        // Remplacer la facture existante
        existingInvoices[existingIndex] = invoice;
        debugPrint('📝 Facture mise à jour');
      } else {
        // Ajouter la nouvelle facture
        existingInvoices.add(invoice);
        debugPrint('📝 Nouvelle facture ajoutée');
      }

      // Sauvegarder la liste complète
      await saveInvoices(existingInvoices);

      debugPrint('✅ Sauvegarde locale optimisée terminée');
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde locale optimisée: $e');
      rethrow;
    }
  }

  // Sauvegarder une facture localement (méthode classique - fallback)
  Future<void> _saveInvoiceLocally(Invoice invoice) async {
    try {
      debugPrint('🔄 Sauvegarde locale classique (fallback)...');
      // Charger directement depuis SharedPreferences pour éviter la migration
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_invoicesKey);

      List<Invoice> invoices = [];
      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        invoices = jsonList.map((json) => Invoice.fromJson(json)).toList();
      }

      // Vérifier si la facture existe déjà
      final existingIndex = invoices.indexWhere((inv) => inv.id == invoice.id);

      if (existingIndex != -1) {
        // Mettre à jour la facture existante
        invoices[existingIndex] = invoice;
      } else {
        // Ajouter la nouvelle facture
        invoices.add(invoice);
      }

      // Sauvegarder dans SharedPreferences
      final updatedJsonString = jsonEncode(
        invoices.map((inv) => inv.toJson()).toList(),
      );
      await prefs.setString(_invoicesKey, updatedJsonString);
      debugPrint('✅ Sauvegarde locale classique terminée');
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde locale classique: $e');
      rethrow;
    }
  }

  // Ajouter une nouvelle facture avec gamification
  Future<Map<String, dynamic>> addInvoiceWithGamification(
    Invoice invoice,
  ) async {
    final newInvoice = await addInvoice(invoice);

    // Déduire le stock immédiatement pour tous les articles en stock
    // (indépendamment du statut de la facture)
    await _deductStockForInvoice(newInvoice);

    // Déclencher la gamification seulement pour les factures payées
    GamificationReward? reward;
    if (newInvoice.status == InvoiceStatus.payee) {
      reward = await GamificationService.instance.processNewInvoice(newInvoice);
    }

    // S'assurer que les notifications sont bien envoyées après toutes les opérations
    DataChangeNotifier.instance.notifyInvoicesChanged();
    debugPrint('🔔 Notification finale envoyée après gamification');

    return {'invoice': newInvoice, 'reward': reward};
  }

  // Mettre à jour une facture existante
  Future<void> updateInvoice(Invoice updatedInvoice) async {
    final mobileOptimization = MobileOptimizationService.instance;
    final shouldOptimize = await mobileOptimization.shouldUseOptimizedMode();

    if (shouldOptimize) {
      debugPrint('📱 Mode mobile optimisé - Update');
      // Sur mobile : sauvegarde locale d'abord, puis notification
      await _saveInvoiceLocally(updatedInvoice);
      DataChangeNotifier.instance.notifyInvoicesChanged();

      // Firebase en arrière-plan
      Future.microtask(() async {
        try {
          await FirebaseService.instance.updateInvoice(updatedInvoice);
          debugPrint('✅ Update Firebase réussie (arrière-plan)');
        } catch (e) {
          debugPrint('⚠️ Update Firebase échouée (non critique): $e');
        }
      });
    } else {
      debugPrint('💻 Mode desktop standard - Update');
      // Sur desktop : Firebase puis local
      try {
        await FirebaseService.instance.updateInvoice(updatedInvoice);
      } catch (e) {
        debugPrint('Firebase update failed, updating locally: $e');
      }

      await _saveInvoiceLocally(updatedInvoice);
      DataChangeNotifier.instance.notifyInvoicesChanged();
    }

    debugPrint('📢 Notification de changement envoyée (update)');
  }

  // Déduire le stock des produits pour une facture (appelé immédiatement à la création)
  Future<void> _deductStockForInvoice(Invoice invoice) async {
    try {
      debugPrint('📦 Déduction du stock pour la facture ${invoice.id}');
      final inventoryService = InventoryService.instance;
      bool stockUpdated = false;

      for (final item in invoice.items) {
        // Vérifier si l'article provient du stock
        if (item.isFromStock &&
            item.productId != null &&
            item.productId!.isNotEmpty) {
          try {
            // Récupérer le produit actuel
            final products = await inventoryService.getProducts();
            final product =
                products.where((p) => p.id == item.productId).firstOrNull;

            if (product != null &&
                product.quantity >= 0 &&
                item.quantity >= 0) {
              // Calculer la nouvelle quantité avec vérification de null safety
              final currentQuantity = product.quantity;
              final itemQuantity = item.quantity;

              final newQuantity =
                  (currentQuantity - itemQuantity)
                      .clamp(0, double.infinity)
                      .toInt();

              // Mettre à jour le produit avec la nouvelle quantité
              final updatedProduct = product.copyWith(quantity: newQuantity);
              await inventoryService.updateProduct(updatedProduct);

              debugPrint(
                '✅ Stock déduit: ${product.name} ($currentQuantity -> $newQuantity)',
              );
              stockUpdated = true;
            } else {
              debugPrint(
                '⚠️ Produit non trouvé ou quantités invalides: ${item.productId}',
              );
            }
          } catch (e) {
            debugPrint('❌ Erreur déduction stock pour ${item.name}: $e');
          }
        }
      }

      // Notifier les changements d'inventaire si du stock a été mis à jour
      if (stockUpdated) {
        DataChangeNotifier.instance.notifyProductsChanged();
        debugPrint('🔔 Notification envoyée: Stock mis à jour');
      }
    } catch (e) {
      debugPrint('❌ Erreur générale déduction stock: $e');
    }
  }

  // Mettre à jour une facture avec gamification
  Future<GamificationReward?> updateInvoiceWithGamification(
    Invoice originalInvoice,
    Invoice updatedInvoice,
  ) async {
    await updateInvoice(updatedInvoice);

    // Déclencher la gamification si le statut passe à "payée"
    if (originalInvoice.status != InvoiceStatus.payee &&
        updatedInvoice.status == InvoiceStatus.payee) {
      // Note: Le stock a déjà été déduit à la création de la facture
      // Pas besoin de le déduire à nouveau ici

      final reward = await GamificationService.instance.processNewInvoice(
        updatedInvoice,
      );

      // S'assurer que les notifications sont bien envoyées après toutes les opérations
      DataChangeNotifier.instance.notifyInvoicesChanged();
      debugPrint(
        '🔔 Notification finale envoyée après mise à jour avec gamification',
      );

      return reward;
    }

    return null;
  }

  // Supprimer une facture
  Future<void> deleteInvoice(String invoiceId) async {
    final mobileOptimization = MobileOptimizationService.instance;
    final shouldOptimize = await mobileOptimization.shouldUseOptimizedMode();

    if (shouldOptimize) {
      debugPrint('📱 Mode mobile optimisé - Delete');
      // Sur mobile : suppression locale d'abord, puis notification
      await _deleteInvoiceLocally(invoiceId);
      DataChangeNotifier.instance.notifyInvoicesChanged();

      // Firebase en arrière-plan
      Future.microtask(() async {
        try {
          await FirebaseService.instance.deleteInvoice(invoiceId);
          debugPrint('✅ Delete Firebase réussie (arrière-plan)');
        } catch (e) {
          debugPrint('⚠️ Delete Firebase échouée (non critique): $e');
        }
      });
    } else {
      debugPrint('💻 Mode desktop standard - Delete');
      // Sur desktop : Firebase puis local
      try {
        await FirebaseService.instance.deleteInvoice(invoiceId);
      } catch (e) {
        debugPrint('Firebase delete failed, deleting locally: $e');
      }

      await _deleteInvoiceLocally(invoiceId);
      DataChangeNotifier.instance.notifyInvoicesChanged();
    }

    debugPrint('📢 Notification de changement envoyée (delete)');
  }

  // Supprimer une facture localement
  Future<void> _deleteInvoiceLocally(String invoiceId) async {
    try {
      // Charger directement depuis SharedPreferences pour éviter la migration
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_invoicesKey);

      List<Invoice> invoices = [];
      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        invoices = jsonList.map((json) => Invoice.fromJson(json)).toList();
      }

      invoices.removeWhere((invoice) => invoice.id == invoiceId);

      // Sauvegarder dans SharedPreferences
      final updatedJsonString = jsonEncode(
        invoices.map((inv) => inv.toJson()).toList(),
      );
      await prefs.setString(_invoicesKey, updatedJsonString);
    } catch (e) {
      print('Error deleting invoice locally: $e');
      rethrow;
    }
  }

  // Obtenir une facture par ID
  Future<Invoice?> getInvoiceById(String id) async {
    final invoices = await InvoiceService.loadInvoices();
    try {
      return invoices.firstWhere((invoice) => invoice.id == id);
    } catch (e) {
      return null;
    }
  }

  // Filtrer les factures par statut
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    final invoices = await InvoiceService.loadInvoices();
    return invoices.where((invoice) => invoice.status == status).toList();
  }

  // Rechercher des factures par nom de client
  Future<List<Invoice>> searchInvoicesByClientName(String query) async {
    final invoices = await InvoiceService.loadInvoices();
    final lowerQuery = query.toLowerCase();
    return invoices
        .where(
          (invoice) => invoice.clientName.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  // Obtenir les statistiques des factures
  Future<Map<String, dynamic>> getInvoiceStats() async {
    final invoices = await InvoiceService.loadInvoices();

    double totalRevenue = 0;
    int paidCount = 0;
    int pendingCount = 0;
    int cancelledCount = 0;

    // Pour les communes les plus sollicitées
    Map<String, int> deliveryLocationCounts = {};
    final DateTime now = DateTime.now();
    final DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);

    for (final invoice in invoices) {
      // Compter les livraisons par commune pour le mois en cours
      if (invoice.createdAt.isAfter(firstDayOfMonth)) {
        deliveryLocationCounts[invoice.deliveryLocation] =
            (deliveryLocationCounts[invoice.deliveryLocation] ?? 0) + 1;
      }

      switch (invoice.status) {
        case InvoiceStatus.payee:
          // CA = prix des articles - remises (sans frais de livraison)
          double subtotal = InvoiceService().calculateSubtotal(invoice.items);
          totalRevenue += subtotal;
          paidCount++;
          break;
        case InvoiceStatus.enAttente:
          pendingCount++;
          break;
        case InvoiceStatus.annulee:
          cancelledCount++;
          break;
      }
    }

    // Trier les communes par nombre de livraisons (décroissant)
    var sortedLocations =
        deliveryLocationCounts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    // Prendre les 4 premières communes
    var topLocations = sortedLocations.take(4).toList();

    return {
      'totalInvoices': invoices.length,
      'totalRevenue': totalRevenue,
      'paidCount': paidCount,
      'pendingCount': pendingCount,
      'cancelledCount': cancelledCount,
      'topDeliveryLocations':
          topLocations
              .map((e) => {'location': e.key, 'count': e.value})
              .toList(),
    };
  }

  // Générer un ID unique pour les articles
  String generateItemId() {
    return _uuid.v4();
  }

  // Calculer le sous-total des articles
  double calculateSubtotal(List<InvoiceItem> items) {
    return items.fold(0, (sum, item) => sum + item.total);
  }

  // Calculer le total final
  double calculateTotal({
    required double subtotal,
    required double deliveryPrice,
    required double discountAmount, // Ajout du paramètre de remise
    double tipAmount = 0.0, // Ajout du paramètre de pourboire
    required double advance,
  }) {
    return subtotal + deliveryPrice + tipAmount - discountAmount - advance;
  }

  // Obtenir les statistiques de performance des employés pour le mois en cours
  Future<Map<String, dynamic>> getEmployeePerformanceStats() async {
    final invoices = await InvoiceService.loadInvoices();
    final DateTime now = DateTime.now();
    final DateTime startOfMonth = DateTime(now.year, now.month, 1);
    final DateTime endOfMonth = DateTime(
      now.year,
      now.month + 1,
      0,
      23,
      59,
      59,
    );

    // Filtrer les factures payées du mois en cours
    final monthlyPaidInvoices =
        invoices
            .where(
              (invoice) =>
                  invoice.status == InvoiceStatus.payee &&
                  invoice.createdAt.isAfter(startOfMonth) &&
                  invoice.createdAt.isBefore(endOfMonth),
            )
            .toList();

    // Grouper par client et calculer le total par client
    Map<String, double> clientTotals = {};
    for (final invoice in monthlyPaidInvoices) {
      final clientKey = '${invoice.clientName}_${invoice.clientNumber}';
      final subtotal = calculateSubtotal(invoice.items);
      clientTotals[clientKey] = (clientTotals[clientKey] ?? 0) + subtotal;
    }

    // Compter les clients valides (commande ≥ 3000 FCFA)
    const double minimumOrderAmount = 3000.0;
    final validClients =
        clientTotals.values
            .where((total) => total >= minimumOrderAmount)
            .length;

    // Calculer le salaire selon les nouveaux paramètres
    const double fixedSalary = 60000.0;
    const double maxBonus = 20000.0;
    const int clientObjective = 200;

    final double bonusPercentage = (validClients / clientObjective).clamp(
      0.0,
      1.0,
    );
    final double bonus = bonusPercentage * maxBonus;
    final double totalSalary = fixedSalary + bonus;

    // Calculer le CA total du mois
    final double monthlyRevenue = monthlyPaidInvoices.fold(
      0.0,
      (sum, invoice) => sum + calculateSubtotal(invoice.items),
    );

    return {
      'validClients': validClients,
      'clientObjective': clientObjective,
      'fixedSalary': fixedSalary,
      'bonus': bonus,
      'maxBonus': maxBonus,
      'totalSalary': totalSalary,
      'bonusPercentage': bonusPercentage * 100,
      'monthlyRevenue': monthlyRevenue,
      'totalInvoices': monthlyPaidInvoices.length,
      'averageOrderValue':
          monthlyPaidInvoices.isNotEmpty
              ? monthlyRevenue / monthlyPaidInvoices.length
              : 0.0,
      'objectiveProgress': (validClients / clientObjective * 100).clamp(
        0.0,
        100.0,
      ),
    };
  }
}
