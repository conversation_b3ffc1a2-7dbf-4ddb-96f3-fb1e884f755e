import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/segmented_backup_service.dart';

class SegmentedBackupPage extends StatefulWidget {
  const SegmentedBackupPage({super.key});

  @override
  State<SegmentedBackupPage> createState() => _SegmentedBackupPageState();
}

class _SegmentedBackupPageState extends State<SegmentedBackupPage> {
  // Configuration de sauvegarde
  final Set<BackupDataType> _selectedDataTypes = {};
  BackupPeriod? _selectedPeriod;
  bool _includeImages = false;
  bool _compressData = true;

  // État de l'interface
  bool _isCreatingBackup = false;
  double _progress = 0.0;
  String _currentOperation = '';
  Map<String, dynamic>? _sizeEstimate;
  List<BackupPeriod> _availablePeriods = [];

  @override
  void initState() {
    super.initState();
    _loadAvailablePeriods();
    _selectedPeriod = BackupPeriod.all();
  }

  Future<void> _loadAvailablePeriods() async {
    final periods = await SegmentedBackupService.getAvailableMonths(
      BackupDataType.invoices, // Utiliser les factures comme référence
    );
    setState(() {
      _availablePeriods = periods;
    });
  }

  Future<void> _updateSizeEstimate() async {
    if (_selectedDataTypes.isEmpty) {
      setState(() {
        _sizeEstimate = null;
      });
      return;
    }

    final config = SegmentedBackupConfig(
      dataTypes: _selectedDataTypes.toList(),
      period: _selectedPeriod,
      includeImages: _includeImages,
      compressData: _compressData,
    );

    final estimate = await SegmentedBackupService.estimateSegmentedBackupSize(config);
    setState(() {
      _sizeEstimate = estimate;
    });
  }

  Future<void> _createSegmentedBackup() async {
    if (_selectedDataTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner au moins un type de données'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isCreatingBackup = true;
      _progress = 0.0;
      _currentOperation = 'Initialisation...';
    });

    try {
      final config = SegmentedBackupConfig(
        dataTypes: _selectedDataTypes.toList(),
        period: _selectedPeriod,
        includeImages: _includeImages,
        compressData: _compressData,
      );

      final backup = await SegmentedBackupService.createSegmentedBackup(
        config,
        onProgress: (progress, operation) {
          if (mounted) {
            setState(() {
              _progress = progress;
              _currentOperation = operation;
            });
          }
        },
      );

      // Générer le nom du fichier
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final dataTypesStr = _selectedDataTypes.map((e) => e.key).join('_');
      final periodStr = _selectedPeriod?.displayName.replaceAll(' ', '_') ?? 'all';
      final fileName = 'hcp_backup_${dataTypesStr}_${periodStr}_$timestamp.json';

      final jsonString = jsonEncode(backup);
      final sizeKB = (jsonString.length / 1024).toStringAsFixed(1);

      setState(() {
        _currentOperation = 'Sauvegarde segmentée terminée! Taille: ${sizeKB}KB';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sauvegarde segmentée créée: $fileName (${sizeKB}KB)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCreatingBackup = false;
        _progress = 0.0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sauvegarde Segmentée'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sélection des types de données
            _buildDataTypeSelection(),
            const SizedBox(height: 16),
            
            // Sélection de la période
            _buildPeriodSelection(),
            const SizedBox(height: 16),
            
            // Options avancées
            _buildAdvancedOptions(),
            const SizedBox(height: 16),
            
            // Estimation de taille
            if (_sizeEstimate != null) ...[
              _buildSizeEstimate(),
              const SizedBox(height: 16),
            ],
            
            // Barre de progression
            if (_isCreatingBackup) ...[
              _buildProgressCard(),
              const SizedBox(height: 16),
            ],
            
            // Bouton de création
            _buildCreateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildDataTypeSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.checklist, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  'Types de Données à Sauvegarder',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...BackupDataType.values.map((dataType) {
              return CheckboxListTile(
                title: Text(dataType.displayName),
                subtitle: Text(_getDataTypeDescription(dataType)),
                value: _selectedDataTypes.contains(dataType),
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedDataTypes.add(dataType);
                    } else {
                      _selectedDataTypes.remove(dataType);
                    }
                  });
                  _updateSizeEstimate();
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: Colors.green[700]),
                const SizedBox(width: 8),
                Text(
                  'Période de Sauvegarde',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<BackupPeriod>(
              value: _selectedPeriod,
              decoration: const InputDecoration(
                labelText: 'Sélectionner une période',
                border: OutlineInputBorder(),
              ),
              items: _availablePeriods.map((period) {
                return DropdownMenuItem(
                  value: period,
                  child: Text(period.displayName),
                );
              }).toList(),
              onChanged: (BackupPeriod? value) {
                setState(() {
                  _selectedPeriod = value;
                });
                _updateSizeEstimate();
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Note: Le filtre par période s\'applique aux factures, tâches et colis',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.orange[700]),
                const SizedBox(width: 8),
                Text(
                  'Options Avancées',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            SwitchListTile(
              title: const Text('Inclure les images'),
              subtitle: const Text('Images des produits et signatures des factures'),
              value: _includeImages,
              onChanged: (bool value) {
                setState(() {
                  _includeImages = value;
                });
                _updateSizeEstimate();
              },
            ),
            SwitchListTile(
              title: const Text('Compression des données'),
              subtitle: const Text('Réduire la taille du fichier de sauvegarde'),
              value: _compressData,
              onChanged: (bool value) {
                setState(() {
                  _compressData = value;
                });
                _updateSizeEstimate();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSizeEstimate() {
    final estimates = _sizeEstimate!['estimates'] as Map<String, dynamic>;
    final totalSizeKB = _sizeEstimate!['totalSizeKB'] as int;

    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  'Estimation de Taille',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...estimates.entries.map((entry) {
              final dataType = BackupDataType.values.firstWhere(
                (dt) => dt.key == entry.key,
              );
              final sizeKB = (entry.value as int / 1024).toStringAsFixed(1);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(dataType.displayName),
                    Text('${sizeKB}KB'),
                  ],
                ),
              );
            }),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total estimé',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${totalSizeKB}KB',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Création en cours...',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[700]!),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_progress * 100).toStringAsFixed(1)}% - $_currentOperation',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isCreatingBackup || _selectedDataTypes.isEmpty 
          ? null 
          : _createSegmentedBackup,
        icon: const Icon(Icons.backup),
        label: Text(_isCreatingBackup 
          ? 'Création en cours...' 
          : 'Créer Sauvegarde Segmentée'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[700],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  String _getDataTypeDescription(BackupDataType dataType) {
    switch (dataType) {
      case BackupDataType.products:
        return 'Inventaire des produits et stock';
      case BackupDataType.categories:
        return 'Catégories de produits';
      case BackupDataType.invoices:
        return 'Factures et devis clients';
      case BackupDataType.tasks:
        return 'Tâches et rappels';
      case BackupDataType.colis:
        return 'Gestion des livraisons';
    }
  }
}
