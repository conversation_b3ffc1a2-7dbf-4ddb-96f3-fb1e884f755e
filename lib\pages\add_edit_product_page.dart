import 'dart:io';
import 'package:flutter/material.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart';
import 'package:general_hcp_crm/services/inventory_service.dart';
import 'package:image_picker/image_picker.dart'; // Pour la sélection d'images

class AddEditProductPage extends StatefulWidget {
  final Product?
  product; // Null si c'est un ajout, non-null si c'est une modification

  const AddEditProductPage({super.key, this.product});

  @override
  State<AddEditProductPage> createState() => _AddEditProductPageState();
}

class _AddEditProductPageState extends State<AddEditProductPage> {
  final _formKey = GlobalKey<FormState>();
  final InventoryService _inventoryService = InventoryService.instance;

  late TextEditingController _nameController;
  late TextEditingController _priceController;
  late TextEditingController _quantityController;
  late TextEditingController _descriptionController;

  List<Category> _categories = [];
  Category? _selectedCategory;
  File? _imageFile; // Pour l'image sélectionnée localement
  String? _networkImageUrl; // Pour l'URL de l'image existante

  bool _isLoading = false;
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.product != null;

    _nameController = TextEditingController(text: widget.product?.name ?? '');
    _priceController = TextEditingController(
      text: widget.product?.price.toString() ?? '',
    );
    _quantityController = TextEditingController(
      text: widget.product?.quantity.toString() ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.product?.description ?? '',
    );
    _networkImageUrl = widget.product?.imageUrl;

    _loadCategories();
  }

  Future<void> _loadCategories() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      debugPrint('🔄 AddEditProductPage: Chargement des catégories...');

      // Timeout pour éviter le chargement infini
      final categories = await _inventoryService.getCategories().timeout(
        const Duration(seconds: 10),
      );

      if (!mounted) return;

      setState(() {
        _categories = categories;
        debugPrint('✅ ${_categories.length} catégories chargées');

        // Sélectionner la catégorie existante si en mode édition
        if (_isEditMode && widget.product?.categoryId != null) {
          try {
            _selectedCategory = _categories.firstWhere(
              (cat) => cat.id == widget.product!.categoryId,
            );
            debugPrint('📌 Catégorie sélectionnée: ${_selectedCategory?.name}');
          } catch (e) {
            _selectedCategory = null;
            debugPrint('⚠️ Catégorie non trouvée pour le produit');
          }
        }
      });
    } catch (e) {
      debugPrint('❌ Erreur chargement catégories: $e');

      if (mounted) {
        // En cas d'erreur, créer au moins une catégorie par défaut
        setState(() {
          _categories = [Category(id: 'default', name: 'Général')];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur de chargement, catégorie par défaut créée'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      // Corrected: Instantiated ImagePicker before calling pickImage
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: source);
      if (pickedFile != null) {
        setState(() {
          _imageFile = File(pickedFile.path);
          _networkImageUrl =
              null; // On remplace l'image réseau si une nouvelle est choisie
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          // Corrected unnecessary escape
          SnackBar(
            content: Text('Erreur de sélection d\'image: ${e.toString()}'),
          ),
        );
      }
    }
  }

  void _showImageSourceActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Galerie'),
                onTap: () {
                  _pickImage(ImageSource.gallery);
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Appareil photo'),
                onTap: () {
                  _pickImage(ImageSource.camera);
                  Navigator.of(context).pop();
                },
              ),
              if (_imageFile != null || _networkImageUrl != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'Supprimer l\'image',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    setState(() {
                      _imageFile = null;
                      _networkImageUrl = null;
                    });
                    Navigator.of(context).pop();
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _saveProduct() async {
    if (_formKey.currentState!.validate() && _selectedCategory != null) {
      setState(() => _isLoading = true);

      String? finalImageUrl = _networkImageUrl;
      if (_imageFile != null) {
        // try {
        //   finalImageUrl = await _inventoryService.uploadProductImage(_imageFile!);
        // } catch (e) {
        //   if (mounted) {
        //     ScaffoldMessenger.of(context).showSnackBar(
        //       SnackBar(content: Text('Error uploading image: ${e.toString()}')),
        //     );
        //   }
        //   // Continue with save even if image upload fails
        // }
        // Pour l'instant, on simule en utilisant le chemin local
        finalImageUrl = _imageFile?.path;
      } else {
        finalImageUrl =
            _networkImageUrl; // Conserver l'image réseau si aucune nouvelle image n'est sélectionnée
      }

      final productData = Product(
        id:
            _isEditMode
                ? widget.product!.id
                : '', // L'ID sera généré par le service si ajout
        name: _nameController.text,
        price: double.tryParse(_priceController.text) ?? 0.0,
        quantity: int.tryParse(_quantityController.text) ?? 0,
        description: _descriptionController.text,
        categoryId: _selectedCategory!.id,
        imageUrl: finalImageUrl,
      );

      try {
        if (_isEditMode) {
          await _inventoryService.updateProduct(productData);
        } else {
          await _inventoryService.addProduct(productData);
        }
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Produit ${_isEditMode ? 'modifié' : 'ajouté'} avec succès!',
              ),
            ),
          );
          Navigator.pop(context, true); // Retourne true pour indiquer un succès
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la sauvegarde: ${e.toString()}'),
            ),
          );
        }
      }
      setState(() => _isLoading = false);
    } else if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez sélectionner une catégorie.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? 'Modifier le Produit' : 'Ajouter un Produit'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        actions: [
          // Bouton de diagnostic rapide
          if (!_isEditMode)
            IconButton(
              onPressed: () async {
                final messenger = ScaffoldMessenger.of(context);
                messenger.showSnackBar(
                  const SnackBar(
                    content: Text('Test de chargement des catégories...'),
                  ),
                );
                try {
                  final categories = await _inventoryService.getCategories();
                  if (mounted) {
                    messenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          '✅ ${categories.length} catégories trouvées',
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    messenger.showSnackBar(
                      SnackBar(
                        content: Text('❌ Erreur: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              icon: const Icon(Icons.bug_report),
              tooltip: 'Test de diagnostic',
            ),
        ],
      ),
      body:
          _isLoading &&
                  _categories
                      .isEmpty // Afficher le chargement seulement si les catégories ne sont pas encore là
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      _buildImagePicker(),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Nom du produit',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer le nom du produit';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'Prix',
                          border: OutlineInputBorder(),
                          prefixText: '€ ',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer le prix';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez entrer un prix valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _quantityController,
                        decoration: const InputDecoration(
                          labelText: 'Quantité en stock',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer la quantité';
                          }
                          if (int.tryParse(value) == null) {
                            return 'Veuillez entrer une quantité valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<Category>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'Catégorie',
                          border: OutlineInputBorder(),
                        ),
                        items:
                            _categories.map((Category category) {
                              return DropdownMenuItem<Category>(
                                value: category,
                                child: Text(category.name),
                              );
                            }).toList(),
                        onChanged: (Category? newValue) {
                          setState(() {
                            _selectedCategory = newValue;
                          });
                        },
                        validator:
                            (value) =>
                                value == null
                                    ? 'Veuillez sélectionner une catégorie'
                                    : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description (optionnel)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _saveProduct,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                                : Text(
                                  _isEditMode
                                      ? 'Enregistrer les modifications'
                                      : 'Ajouter le produit',
                                ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildImagePicker() {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: () => _showImageSourceActionSheet(context),
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: _buildImageWidget(),
            ),
          ),
          const SizedBox(height: 8),
          TextButton.icon(
            icon: const Icon(Icons.image_search),
            label: const Text('Choisir une image'),
            onPressed: () => _showImageSourceActionSheet(context),
          ),
        ],
      ),
    );
  }

  Widget _buildImageWidget() {
    // Priorité à l'image locale nouvellement sélectionnée
    if (_imageFile != null) {
      try {
        return ClipRRect(
          borderRadius: BorderRadius.circular(7),
          child: Image.file(
            _imageFile!,
            fit: BoxFit.cover,
            width: 150,
            height: 150,
            errorBuilder: (context, error, stackTrace) {
              return const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, size: 40, color: Colors.red),
                  SizedBox(height: 4),
                  Text(
                    'Erreur image',
                    style: TextStyle(fontSize: 12, color: Colors.red),
                  ),
                ],
              );
            },
          ),
        );
      } catch (e) {
        return const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 40, color: Colors.red),
            SizedBox(height: 4),
            Text(
              'Fichier introuvable',
              style: TextStyle(fontSize: 12, color: Colors.red),
            ),
          ],
        );
      }
    }

    // Sinon, afficher l'image réseau si disponible
    if (_networkImageUrl != null && _networkImageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: Image.network(
          _networkImageUrl!,
          fit: BoxFit.cover,
          width: 150,
          height: 150,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 40, color: Colors.orange),
                SizedBox(height: 4),
                Text(
                  'Image indisponible',
                  style: TextStyle(fontSize: 12, color: Colors.orange),
                ),
              ],
            );
          },
        ),
      );
    }

    // Aucune image sélectionnée
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.add_a_photo, size: 50, color: Colors.grey),
        SizedBox(height: 8),
        Text(
          'Ajouter une image',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
