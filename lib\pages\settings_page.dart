import 'package:flutter/material.dart';
import '../services/offline_config_service.dart';
import '../services/sync_service.dart';
import 'app_info_page.dart';
import 'legacy_migration_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _isOfflineModeEnabled = true;
  bool _isSyncing = false;
  Map<String, bool> _configStatus = {};
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final offlineMode =
          await OfflineConfigService.instance.isOfflineModeEnabled();
      final configStatus =
          await OfflineConfigService.instance.getConfigStatus();

      setState(() {
        _isOfflineModeEnabled = offlineMode;
        _configStatus = configStatus;
      });
    } catch (e) {
      _showErrorSnackBar('Erreur lors du chargement des paramètres: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOfflineModeCard(),
            const SizedBox(height: 16),
            _buildSyncCard(),
            const SizedBox(height: 16),
            _buildDataMigrationCard(),
            const SizedBox(height: 16),
            _buildConfigStatusCard(),
            const SizedBox(height: 16),
            _buildAppInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildOfflineModeCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isOfflineModeEnabled ? Icons.wifi_off : Icons.wifi,
                  color:
                      _isOfflineModeEnabled
                          ? Colors.orange[600]
                          : Colors.green[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Mode de fonctionnement',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                _isOfflineModeEnabled ? 'Mode Offline' : 'Mode Online',
              ),
              subtitle: Text(
                _isOfflineModeEnabled
                    ? 'Application utilisable sans connexion internet'
                    : 'Synchronisation automatique activée',
              ),
              value: !_isOfflineModeEnabled,
              onChanged: _toggleOfflineMode,
              activeColor: Colors.green[600],
            ),
            if (_isOfflineModeEnabled) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.orange[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'En mode offline, toutes les synchronisations automatiques sont désactivées.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange[800],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSyncCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.sync, color: Colors.blue[600], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Synchronisation',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_lastSyncTime != null) ...[
              Text(
                'Dernière synchronisation: ${_formatDateTime(_lastSyncTime!)}',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 12),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _isOfflineModeEnabled
                        ? null
                        : (_isSyncing ? null : _performManualSync),
                icon:
                    _isSyncing
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.sync),
                label: Text(
                  _isSyncing ? 'Synchronisation...' : 'Synchroniser maintenant',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            if (_isOfflineModeEnabled) ...[
              const SizedBox(height: 8),
              Text(
                'Activez le mode online pour synchroniser',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConfigStatusCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'État de la configuration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._configStatus.entries.map(
              (entry) => _buildStatusRow(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String key, bool value) {
    final labels = {
      'offlineModeEnabled': 'Mode offline',
      'autoSyncDisabled': 'Sync auto désactivé',
      'firebaseDisabled': 'Firebase désactivé',
      'networkOperationsDisabled': 'Réseau désactivé',
      'manualSyncOnly': 'Sync manuelle uniquement',
    };

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green[600] : Colors.red[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(labels[key] ?? key, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildAppInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ListTile(
        leading: Icon(Icons.info_outline, color: Colors.blue[600]),
        title: const Text('Informations de l\'application'),
        subtitle: const Text('Version, changelog et détails'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AppInfoPage()),
          );
        },
      ),
    );
  }

  Future<void> _toggleOfflineMode(bool enableOnlineMode) async {
    try {
      setState(() {
        _isOfflineModeEnabled = !enableOnlineMode;
      });

      await OfflineConfigService.instance.setOfflineModeEnabled(
        !enableOnlineMode,
      );
      await _loadSettings();

      _showSuccessSnackBar(
        enableOnlineMode
            ? 'Mode online activé - Synchronisation disponible'
            : 'Mode offline activé - Application utilisable sans internet',
      );
    } catch (e) {
      _showErrorSnackBar('Erreur lors du changement de mode: $e');
      await _loadSettings(); // Recharger l'état en cas d'erreur
    }
  }

  Future<void> _performManualSync() async {
    if (_isOfflineModeEnabled) {
      _showErrorSnackBar('Activez le mode online pour synchroniser');
      return;
    }

    setState(() {
      _isSyncing = true;
    });

    try {
      // Initialiser et synchroniser
      await SyncService.instance.initialize();
      final success = await SyncService.instance.syncNow();

      setState(() {
        _lastSyncTime = DateTime.now();
      });

      if (success) {
        _showSuccessSnackBar('Synchronisation terminée avec succès');
      } else {
        _showErrorSnackBar(
          'Synchronisation échouée - Vérifiez votre connexion',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la synchronisation: $e');
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  Widget _buildDataMigrationCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sync_alt,
                  color: Colors.purple[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Migration des Données',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Migrez vos anciennes données depuis un fichier JSON de sauvegarde vers le nouveau format de l\'application.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const LegacyMigrationPage(),
                    ),
                  );
                },
                icon: const Icon(Icons.upload_file),
                label: const Text('Migrer les Données Legacy'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Supporte les produits, catégories, factures et tâches depuis l\'ancien format JSON.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} à ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green[600],
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red[600],
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
