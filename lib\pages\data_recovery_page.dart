import 'package:flutter/material.dart';
import '../services/data_recovery_service.dart';

import '../services/unified_sync_service.dart';

class DataRecoveryPage extends StatefulWidget {
  const DataRecoveryPage({super.key});

  @override
  State<DataRecoveryPage> createState() => _DataRecoveryPageState();
}

class _DataRecoveryPageState extends State<DataRecoveryPage> {
  final DataRecoveryService _recoveryService = DataRecoveryService.instance;
  final UnifiedSyncService _syncService = UnifiedSyncService.instance;

  Map<String, dynamic>? _diagnosis;
  bool _isLoading = false;
  bool _isRecovering = false;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _runDiagnosis();
  }

  Future<void> _runDiagnosis() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final diagnosis = await _recoveryService.diagnoseDataProblems();
      setState(() {
        _diagnosis = diagnosis;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _recoverOldData() async {
    setState(() {
      _isRecovering = true;
    });

    try {
      final success = await _recoveryService.recoverOldData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ Données récupérées avec succès'
                  : '⚠️ Aucune donnée à récupérer',
            ),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );

        if (success) {
          // Relancer le diagnostic
          await _runDiagnosis();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la récupération: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  Future<void> _forceSynchronization() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      await _syncService.forceMigration();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Synchronisation forcée terminée'),
            backgroundColor: Colors.green,
          ),
        );

        // Relancer le diagnostic
        await _runDiagnosis();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la synchronisation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Récupération des Données'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runDiagnosis,
            tooltip: 'Relancer le diagnostic',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Diagnostic en cours...'),
                  ],
                ),
              )
              : _diagnosis == null
              ? const Center(child: Text('Erreur lors du diagnostic'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDiagnosisCard(),
                    const SizedBox(height: 16),
                    _buildLocalDataCard(),
                    const SizedBox(height: 16),
                    _buildFirebaseDataCard(),
                    const SizedBox(height: 16),
                    _buildSyncStatusCard(),
                    const SizedBox(height: 16),
                    _buildProblemsCard(),
                    const SizedBox(height: 16),
                    _buildSolutionsCard(),
                    const SizedBox(height: 16),
                    _buildActionsCard(),
                  ],
                ),
              ),
    );
  }

  Widget _buildDiagnosisCard() {
    final problems = _diagnosis?['problems'] as List<String>? ?? [];
    final hasProblems = problems.isNotEmpty;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasProblems ? Icons.warning : Icons.check_circle,
                  color: hasProblems ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'État du Système',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              hasProblems
                  ? '${problems.length} problème(s) détecté(s)'
                  : 'Aucun problème détecté',
              style: TextStyle(
                color: hasProblems ? Colors.orange : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalDataCard() {
    final localData = _diagnosis?['localData'] as Map<String, dynamic>? ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Données Locales',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildDataRow('Factures', localData['invoices']),
            _buildDataRow('Colis', localData['colis']),
            _buildDataRow('Produits', localData['products']),
            _buildDataRow('Tâches', localData['tasks']),
          ],
        ),
      ),
    );
  }

  Widget _buildFirebaseDataCard() {
    final firebaseData =
        _diagnosis?['firebaseData'] as Map<String, dynamic>? ?? {};
    final isOnline = firebaseData['status'] == 'online';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: isOnline ? Colors.green : Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Données Firebase',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                Chip(
                  label: Text(
                    isOnline ? 'En ligne' : 'Hors ligne',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  backgroundColor: isOnline ? Colors.green : Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (isOnline) ...[
              _buildDataRow('Factures', firebaseData['invoices']),
              _buildDataRow('Colis', firebaseData['colis']),
              _buildDataRow('Produits', firebaseData['products']),
              _buildDataRow('Tâches', firebaseData['tasks']),
            ] else
              const Text(
                'Impossible de vérifier les données Firebase (hors ligne)',
                style: TextStyle(color: Colors.red),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatusCard() {
    final syncStatus = _diagnosis?['syncStatus'] as Map<String, dynamic>? ?? {};
    final migrationComplete = syncStatus['migrationComplete'] ?? false;
    final pendingOps = syncStatus['pendingOperations'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sync,
                  color: migrationComplete ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Statut de Synchronisation',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              'Migration terminée',
              migrationComplete ? 'Oui' : 'Non',
              migrationComplete,
            ),
            if (syncStatus['lastSync'] != null)
              _buildStatusRow(
                'Dernière sync',
                _formatDate(syncStatus['lastSync']),
                true,
              ),
            if (pendingOps > 0)
              _buildStatusRow(
                'Opérations en attente',
                pendingOps.toString(),
                false,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProblemsCard() {
    final problems = _diagnosis?['problems'] as List<String>? ?? [];

    if (problems.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'Aucun problème détecté',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Problèmes Détectés',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...problems.map(
              (problem) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(problem)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionsCard() {
    final solutions = _diagnosis?['solutions'] as List<String>? ?? [];

    if (solutions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Solutions Recommandées',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...solutions.map(
              (solution) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.check, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Expanded(child: Text(solution)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Actions de Récupération',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRecovering ? null : _recoverOldData,
                icon:
                    _isRecovering
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.restore),
                label: Text(
                  _isRecovering
                      ? 'Récupération en cours...'
                      : 'Récupérer les Anciennes Données',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSyncing ? null : _forceSynchronization,
                icon:
                    _isSyncing
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.sync),
                label: Text(
                  _isSyncing
                      ? 'Synchronisation en cours...'
                      : 'Forcer la Synchronisation',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String label, Map<String, dynamic>? data) {
    final count = data?['count'] ?? 0;
    final hasData = data?['hasData'] ?? false;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Text(
                count.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: hasData ? Colors.green : Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                hasData ? Icons.check_circle : Icons.cancel,
                color: hasData ? Colors.green : Colors.grey,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isGood) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isGood ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                isGood ? Icons.check_circle : Icons.warning,
                color: isGood ? Colors.green : Colors.orange,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return 'Il y a ${difference.inDays} jour(s)';
      } else if (difference.inHours > 0) {
        return 'Il y a ${difference.inHours} heure(s)';
      } else {
        return 'Il y a ${difference.inMinutes} minute(s)';
      }
    } catch (e) {
      return dateStr;
    }
  }
}
