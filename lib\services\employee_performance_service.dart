import '../models/invoice.dart';
import 'invoice_service.dart';

/// Service pour gérer les performances des employés
class EmployeePerformanceService {
  static final EmployeePerformanceService _instance = EmployeePerformanceService._internal();
  factory EmployeePerformanceService() => _instance;
  EmployeePerformanceService._internal();

  static EmployeePerformanceService get instance => _instance;

  final InvoiceService _invoiceService = InvoiceService();

  // Paramètres de performance
  static const double _fixedSalary = 60000.0;
  static const double _maxBonus = 20000.0;
  static const int _clientObjective = 200;
  static const double _minimumOrderAmount = 3000.0;

  /// Calculer le salaire basé sur le nombre de clients valides
  double calculateSalary(int validClients) {
    final double bonusPercentage = (validClients / _clientObjective).clamp(0.0, 1.0);
    final double bonus = bonusPercentage * _maxBonus;
    return _fixedSalary + bonus;
  }

  /// Obtenir les statistiques de performance pour le mois en cours
  Future<EmployeePerformanceStats> getCurrentMonthPerformance() async {
    final invoices = await InvoiceService.loadInvoices();
    final DateTime now = DateTime.now();
    final DateTime startOfMonth = DateTime(now.year, now.month, 1);
    final DateTime endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

    return _calculatePerformanceForPeriod(invoices, startOfMonth, endOfMonth);
  }

  /// Obtenir les statistiques de performance pour une période donnée
  Future<EmployeePerformanceStats> getPerformanceForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final invoices = await InvoiceService.loadInvoices();
    return _calculatePerformanceForPeriod(invoices, startDate, endDate);
  }

  /// Calculer les performances pour une période donnée
  EmployeePerformanceStats _calculatePerformanceForPeriod(
    List<Invoice> allInvoices,
    DateTime startDate,
    DateTime endDate,
  ) {
    // Filtrer les factures payées de la période
    final periodPaidInvoices = allInvoices.where((invoice) =>
      invoice.status == InvoiceStatus.payee &&
      invoice.createdAt.isAfter(startDate) &&
      invoice.createdAt.isBefore(endDate)
    ).toList();

    // Grouper par client et calculer le total par client
    Map<String, ClientOrderInfo> clientOrders = {};
    
    for (final invoice in periodPaidInvoices) {
      final clientKey = '${invoice.clientName}_${invoice.clientNumber}';
      final subtotal = _invoiceService.calculateSubtotal(invoice.items);
      
      if (clientOrders.containsKey(clientKey)) {
        clientOrders[clientKey] = clientOrders[clientKey]!.copyWith(
          totalAmount: clientOrders[clientKey]!.totalAmount + subtotal,
          orderCount: clientOrders[clientKey]!.orderCount + 1,
        );
      } else {
        clientOrders[clientKey] = ClientOrderInfo(
          clientName: invoice.clientName,
          clientNumber: invoice.clientNumber,
          totalAmount: subtotal,
          orderCount: 1,
        );
      }
    }

    // Compter les clients valides (commande ≥ 3000 FCFA)
    final validClients = clientOrders.values
        .where((client) => client.totalAmount >= _minimumOrderAmount)
        .length;

    // Calculer le salaire
    final double bonusPercentage = (validClients / _clientObjective).clamp(0.0, 1.0);
    final double bonus = bonusPercentage * _maxBonus;
    final double totalSalary = _fixedSalary + bonus;

    // Calculer le CA total de la période
    final double totalRevenue = periodPaidInvoices.fold(
      0.0,
      (sum, invoice) => sum + _invoiceService.calculateSubtotal(invoice.items),
    );

    // Clients par tranche de commande
    final clientsByOrderRange = _categorizeClientsByOrderAmount(clientOrders.values);

    return EmployeePerformanceStats(
      validClients: validClients,
      totalClients: clientOrders.length,
      clientObjective: _clientObjective,
      fixedSalary: _fixedSalary,
      bonus: bonus,
      maxBonus: _maxBonus,
      totalSalary: totalSalary,
      bonusPercentage: bonusPercentage * 100,
      totalRevenue: totalRevenue,
      totalInvoices: periodPaidInvoices.length,
      averageOrderValue: periodPaidInvoices.isNotEmpty 
          ? totalRevenue / periodPaidInvoices.length 
          : 0.0,
      objectiveProgress: (validClients / _clientObjective * 100).clamp(0.0, 100.0),
      clientsByOrderRange: clientsByOrderRange,
      topClients: _getTopClients(clientOrders.values, 5),
      period: DateRange(startDate, endDate),
    );
  }

  /// Catégoriser les clients par montant de commande
  Map<String, int> _categorizeClientsByOrderAmount(Iterable<ClientOrderInfo> clients) {
    int under1000 = 0;
    int between1000And3000 = 0;
    int between3000And5000 = 0;
    int between5000And10000 = 0;
    int above10000 = 0;

    for (final client in clients) {
      if (client.totalAmount < 1000) {
        under1000++;
      } else if (client.totalAmount < 3000) {
        between1000And3000++;
      } else if (client.totalAmount < 5000) {
        between3000And5000++;
      } else if (client.totalAmount < 10000) {
        between5000And10000++;
      } else {
        above10000++;
      }
    }

    return {
      'under1000': under1000,
      'between1000And3000': between1000And3000,
      'between3000And5000': between3000And5000,
      'between5000And10000': between5000And10000,
      'above10000': above10000,
    };
  }

  /// Obtenir les meilleurs clients
  List<ClientOrderInfo> _getTopClients(Iterable<ClientOrderInfo> clients, int limit) {
    final sortedClients = clients.toList()
      ..sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
    return sortedClients.take(limit).toList();
  }
}

/// Modèle pour les statistiques de performance des employés
class EmployeePerformanceStats {
  final int validClients;
  final int totalClients;
  final int clientObjective;
  final double fixedSalary;
  final double bonus;
  final double maxBonus;
  final double totalSalary;
  final double bonusPercentage;
  final double totalRevenue;
  final int totalInvoices;
  final double averageOrderValue;
  final double objectiveProgress;
  final Map<String, int> clientsByOrderRange;
  final List<ClientOrderInfo> topClients;
  final DateRange period;

  const EmployeePerformanceStats({
    required this.validClients,
    required this.totalClients,
    required this.clientObjective,
    required this.fixedSalary,
    required this.bonus,
    required this.maxBonus,
    required this.totalSalary,
    required this.bonusPercentage,
    required this.totalRevenue,
    required this.totalInvoices,
    required this.averageOrderValue,
    required this.objectiveProgress,
    required this.clientsByOrderRange,
    required this.topClients,
    required this.period,
  });
}

/// Modèle pour les informations de commande client
class ClientOrderInfo {
  final String clientName;
  final String clientNumber;
  final double totalAmount;
  final int orderCount;

  const ClientOrderInfo({
    required this.clientName,
    required this.clientNumber,
    required this.totalAmount,
    required this.orderCount,
  });

  ClientOrderInfo copyWith({
    String? clientName,
    String? clientNumber,
    double? totalAmount,
    int? orderCount,
  }) {
    return ClientOrderInfo(
      clientName: clientName ?? this.clientName,
      clientNumber: clientNumber ?? this.clientNumber,
      totalAmount: totalAmount ?? this.totalAmount,
      orderCount: orderCount ?? this.orderCount,
    );
  }
}

/// Modèle pour une période de dates
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange(this.start, this.end);
}
