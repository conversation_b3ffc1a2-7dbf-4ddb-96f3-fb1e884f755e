import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:archive/archive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';

/// Service de sauvegarde avec compression pour optimiser la taille et la vitesse
class CompressedBackupService {
  static const String _backupVersion = '2.1.0';
  
  /// Créer une sauvegarde compressée optimisée
  static Future<Uint8List> createCompressedBackup() async {
    try {
      debugPrint('📦 Création de sauvegarde compressée...');
      final stopwatch = Stopwatch()..start();

      // 1. Collecter les données de manière optimisée
      final data = await _collectDataOptimized();
      
      // 2. Créer la structure de sauvegarde minimale
      final backup = {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'compressed': true,
        'data': data,
      };

      // 3. Convertir en JSON compact (sans espaces)
      final jsonString = jsonEncode(backup);
      debugPrint('📊 Taille JSON non compressé: ${jsonString.length} caractères');

      // 4. Compresser avec GZip
      final jsonBytes = utf8.encode(jsonString);
      final compressedBytes = GZipEncoder().encode(jsonBytes);
      
      stopwatch.stop();
      final compressionRatio = (compressedBytes!.length / jsonBytes.length * 100).toStringAsFixed(1);
      
      debugPrint('✅ Sauvegarde compressée créée en ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('📊 Taille originale: ${jsonBytes.length} bytes');
      debugPrint('📊 Taille compressée: ${compressedBytes.length} bytes');
      debugPrint('📊 Ratio de compression: $compressionRatio%');

      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      debugPrint('❌ Erreur lors de la création de sauvegarde compressée: $e');
      rethrow;
    }
  }

  /// Restaurer depuis une sauvegarde compressée
  static Future<void> restoreFromCompressedBackup(Uint8List compressedData) async {
    try {
      debugPrint('📂 Restauration depuis sauvegarde compressée...');
      final stopwatch = Stopwatch()..start();

      // 1. Décompresser les données
      final decompressedBytes = GZipDecoder().decodeBytes(compressedData);
      final jsonString = utf8.decode(decompressedBytes);
      
      // 2. Parser le JSON
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // 3. Vérifier la version
      final version = backup['version'] as String?;
      if (version == null) {
        throw Exception('Version de sauvegarde manquante');
      }

      // 4. Restaurer les données de manière optimisée
      await _restoreDataOptimized(backup['data'] as Map<String, dynamic>);
      
      stopwatch.stop();
      debugPrint('✅ Restauration compressée terminée en ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration compressée: $e');
      rethrow;
    }
  }

  /// Collecter les données de manière optimisée
  static Future<Map<String, dynamic>> _collectDataOptimized() async {
    // Utiliser Future.wait pour paralléliser les opérations
    final results = await Future.wait([
      InventoryService.instance.getProducts(),
      InventoryService.instance.getCategories(),
      InvoiceService.loadInvoices(),
      TaskService.instance.getTasks(),
      ColisService.instance.getAllColis(),
    ]);

    final products = results[0] as List<Product>;
    final categories = results[1] as List<Category>;
    final invoices = results[2] as List<Invoice>;
    final tasks = results[3] as List<Task>;
    final colis = results[4] as List<Colis>;

    // Optimiser les données avant sérialisation
    return {
      'products': _optimizeProducts(products),
      'categories': _optimizeCategories(categories),
      'invoices': _optimizeInvoices(invoices),
      'tasks': _optimizeTasks(tasks),
      'colis': _optimizeColis(colis),
    };
  }

  /// Optimiser les produits (supprimer les champs inutiles)
  static List<Map<String, dynamic>> _optimizeProducts(List<Product> products) {
    return products.map((product) {
      final json = product.toJson();
      // Supprimer les champs optionnels vides
      json.removeWhere((key, value) => 
        value == null || 
        (value is String && value.isEmpty) ||
        (value is List && value.isEmpty)
      );
      return json;
    }).toList();
  }

  /// Optimiser les catégories
  static List<Map<String, dynamic>> _optimizeCategories(List<Category> categories) {
    return categories.map((category) {
      final json = category.toJson();
      json.removeWhere((key, value) => 
        value == null || 
        (value is String && value.isEmpty)
      );
      return json;
    }).toList();
  }

  /// Optimiser les factures
  static List<Map<String, dynamic>> _optimizeInvoices(List<Invoice> invoices) {
    return invoices.map((invoice) {
      final json = invoice.toJson();
      // Supprimer les images encodées en base64 si trop volumineuses
      if (json['imageBase64'] != null && 
          (json['imageBase64'] as String).length > 100000) {
        json.remove('imageBase64');
      }
      json.removeWhere((key, value) => 
        value == null || 
        (value is String && value.isEmpty) ||
        (value is List && value.isEmpty)
      );
      return json;
    }).toList();
  }

  /// Optimiser les tâches
  static List<Map<String, dynamic>> _optimizeTasks(List<Task> tasks) {
    return tasks.map((task) {
      final json = task.toJson();
      json.removeWhere((key, value) => 
        value == null || 
        (value is String && value.isEmpty)
      );
      return json;
    }).toList();
  }

  /// Optimiser les colis
  static List<Map<String, dynamic>> _optimizeColis(List<Colis> colis) {
    return colis.map((c) {
      final json = c.toJson();
      json.removeWhere((key, value) => 
        value == null || 
        (value is String && value.isEmpty)
      );
      return json;
    }).toList();
  }

  /// Restaurer les données de manière optimisée
  static Future<void> _restoreDataOptimized(Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();

    // Restaurer en parallèle pour améliorer les performances
    await Future.wait([
      _restoreProducts(data['products'] as List<dynamic>? ?? []),
      _restoreCategories(data['categories'] as List<dynamic>? ?? []),
      _restoreInvoices(data['invoices'] as List<dynamic>? ?? []),
      _restoreTasks(data['tasks'] as List<dynamic>? ?? []),
      _restoreColis(data['colis'] as List<dynamic>? ?? []),
    ]);

    debugPrint('✅ Toutes les données restaurées en parallèle');
  }

  static Future<void> _restoreProducts(List<dynamic> productsData) async {
    if (productsData.isEmpty) return;
    
    final products = productsData
        .map((json) => Product.fromJson(json as Map<String, dynamic>))
        .toList();
    
    final prefs = await SharedPreferences.getInstance();
    final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
    await prefs.setString('inventory_products', productsJson);
    
    debugPrint('✅ ${products.length} produits restaurés');
  }

  static Future<void> _restoreCategories(List<dynamic> categoriesData) async {
    if (categoriesData.isEmpty) return;
    
    final categories = categoriesData
        .map((json) => Category.fromJson(json as Map<String, dynamic>))
        .toList();
    
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(categories.map((c) => c.toJson()).toList());
    await prefs.setString('inventory_categories', categoriesJson);
    
    debugPrint('✅ ${categories.length} catégories restaurées');
  }

  static Future<void> _restoreInvoices(List<dynamic> invoicesData) async {
    if (invoicesData.isEmpty) return;
    
    final invoices = invoicesData
        .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
        .toList();
    
    await InvoiceService.saveInvoices(invoices);
    debugPrint('✅ ${invoices.length} factures restaurées');
  }

  static Future<void> _restoreTasks(List<dynamic> tasksData) async {
    if (tasksData.isEmpty) return;
    
    final tasks = tasksData
        .map((json) => Task.fromJson(json as Map<String, dynamic>))
        .toList();
    
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
    await prefs.setString('tasks', tasksJson);
    
    debugPrint('✅ ${tasks.length} tâches restaurées');
  }

  static Future<void> _restoreColis(List<dynamic> colisData) async {
    if (colisData.isEmpty) return;
    
    final colis = colisData
        .map((json) => Colis.fromJson(json as Map<String, dynamic>))
        .toList();
    
    await ColisService.instance.saveColis(colis);
    debugPrint('✅ ${colis.length} colis restaurés');
  }

  /// Créer une sauvegarde incrémentale (seulement les changements)
  static Future<Uint8List> createIncrementalBackup(DateTime? lastBackupDate) async {
    debugPrint('📦 Création de sauvegarde incrémentale...');
    
    // Collecter seulement les données modifiées depuis la dernière sauvegarde
    final data = await _collectIncrementalData(lastBackupDate);
    
    final backup = {
      'version': _backupVersion,
      'timestamp': DateTime.now().toIso8601String(),
      'incremental': true,
      'lastBackupDate': lastBackupDate?.toIso8601String(),
      'data': data,
    };

    final jsonString = jsonEncode(backup);
    final jsonBytes = utf8.encode(jsonString);
    final compressedBytes = GZipEncoder().encode(jsonBytes);
    
    debugPrint('✅ Sauvegarde incrémentale créée (${compressedBytes!.length} bytes)');
    return Uint8List.fromList(compressedBytes);
  }

  static Future<Map<String, dynamic>> _collectIncrementalData(DateTime? lastBackupDate) async {
    // Pour l'instant, retourner toutes les données
    // Dans une version future, filtrer par date de modification
    return await _collectDataOptimized();
  }
}
