import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart';
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';

/// Service pour gérer la persistance des données de manière cohérente
class DataPersistenceService {
  static DataPersistenceService? _instance;
  static DataPersistenceService get instance {
    _instance ??= DataPersistenceService._internal();
    return _instance!;
  }

  DataPersistenceService._internal();

  // Clés standardisées pour SharedPreferences
  static const String _productsKey = 'inventory_products';
  static const String _categoriesKey = 'inventory_categories';
  static const String _invoicesKey = 'hcp_invoices';
  static const String _tasksKey = 'tasks';
  static const String _colisKey = 'hcp_colis';
  static const String _lastSaveKey = 'last_data_save';
  static const String _dataVersionKey = 'data_version';

  /// Sauvegarder toutes les données de manière atomique
  Future<bool> saveAllData({
    List<Product>? products,
    List<Category>? categories,
    List<Invoice>? invoices,
    List<Task>? tasks,
    List<Colis>? colis,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      debugPrint('💾 Sauvegarde de toutes les données...');

      // Sauvegarder les produits
      if (products != null) {
        final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
        await prefs.setString(_productsKey, productsJson);
        debugPrint('✅ ${products.length} produits sauvegardés');
      }

      // Sauvegarder les catégories
      if (categories != null) {
        final categoriesJson = jsonEncode(categories.map((c) => c.toJson()).toList());
        await prefs.setString(_categoriesKey, categoriesJson);
        debugPrint('✅ ${categories.length} catégories sauvegardées');
      }

      // Sauvegarder les factures
      if (invoices != null) {
        final invoicesJson = jsonEncode(invoices.map((i) => i.toJson()).toList());
        await prefs.setString(_invoicesKey, invoicesJson);
        debugPrint('✅ ${invoices.length} factures sauvegardées');
      }

      // Sauvegarder les tâches
      if (tasks != null) {
        final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
        await prefs.setString(_tasksKey, tasksJson);
        debugPrint('✅ ${tasks.length} tâches sauvegardées');
      }

      // Sauvegarder les colis (format spécial pour ColisService)
      if (colis != null) {
        final colisStringList = colis.map((c) => jsonEncode(c.toJson())).toList();
        await prefs.setStringList(_colisKey, colisStringList);
        debugPrint('✅ ${colis.length} colis sauvegardés');
      }

      // Mettre à jour les métadonnées
      await prefs.setString(_lastSaveKey, DateTime.now().toIso8601String());
      await prefs.setInt(_dataVersionKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('💾 Sauvegarde complète terminée avec succès');
      return true;

    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde: $e');
      return false;
    }
  }

  /// Charger toutes les données
  Future<Map<String, dynamic>> loadAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      debugPrint('📂 Chargement de toutes les données...');

      final result = <String, dynamic>{};

      // Charger les produits
      final productsJson = prefs.getString(_productsKey);
      if (productsJson != null) {
        final productsList = jsonDecode(productsJson) as List<dynamic>;
        result['products'] = productsList.map((p) => Product.fromJson(p)).toList();
        debugPrint('📂 ${result['products'].length} produits chargés');
      } else {
        result['products'] = <Product>[];
      }

      // Charger les catégories
      final categoriesJson = prefs.getString(_categoriesKey);
      if (categoriesJson != null) {
        final categoriesList = jsonDecode(categoriesJson) as List<dynamic>;
        result['categories'] = categoriesList.map((c) => Category.fromJson(c)).toList();
        debugPrint('📂 ${result['categories'].length} catégories chargées');
      } else {
        result['categories'] = <Category>[];
      }

      // Charger les factures
      final invoicesJson = prefs.getString(_invoicesKey);
      if (invoicesJson != null) {
        final invoicesList = jsonDecode(invoicesJson) as List<dynamic>;
        result['invoices'] = invoicesList.map((i) => Invoice.fromJson(i)).toList();
        debugPrint('📂 ${result['invoices'].length} factures chargées');
      } else {
        result['invoices'] = <Invoice>[];
      }

      // Charger les tâches
      final tasksJson = prefs.getString(_tasksKey);
      if (tasksJson != null) {
        final tasksList = jsonDecode(tasksJson) as List<dynamic>;
        result['tasks'] = tasksList.map((t) => Task.fromJson(t)).toList();
        debugPrint('📂 ${result['tasks'].length} tâches chargées');
      } else {
        result['tasks'] = <Task>[];
      }

      // Charger les colis
      final colisStringList = prefs.getStringList(_colisKey);
      if (colisStringList != null) {
        result['colis'] = colisStringList.map((c) => Colis.fromJson(jsonDecode(c))).toList();
        debugPrint('📂 ${result['colis'].length} colis chargés');
      } else {
        result['colis'] = <Colis>[];
      }

      // Métadonnées
      result['lastSave'] = prefs.getString(_lastSaveKey);
      result['dataVersion'] = prefs.getInt(_dataVersionKey);

      debugPrint('📂 Chargement complet terminé avec succès');
      return result;

    } catch (e) {
      debugPrint('❌ Erreur lors du chargement: $e');
      return {
        'products': <Product>[],
        'categories': <Category>[],
        'invoices': <Invoice>[],
        'tasks': <Task>[],
        'colis': <Colis>[],
      };
    }
  }

  /// Vérifier si des données existent
  Future<bool> hasData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final hasProducts = prefs.containsKey(_productsKey);
      final hasCategories = prefs.containsKey(_categoriesKey);
      final hasInvoices = prefs.containsKey(_invoicesKey);
      final hasTasks = prefs.containsKey(_tasksKey);
      final hasColis = prefs.containsKey(_colisKey);

      return hasProducts || hasCategories || hasInvoices || hasTasks || hasColis;
    } catch (e) {
      debugPrint('❌ Erreur lors de la vérification des données: $e');
      return false;
    }
  }

  /// Effacer toutes les données
  Future<bool> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_productsKey);
      await prefs.remove(_categoriesKey);
      await prefs.remove(_invoicesKey);
      await prefs.remove(_tasksKey);
      await prefs.remove(_colisKey);
      await prefs.remove(_lastSaveKey);
      await prefs.remove(_dataVersionKey);

      debugPrint('🗑️ Toutes les données ont été effacées');
      return true;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'effacement: $e');
      return false;
    }
  }

  /// Obtenir les statistiques des données
  Future<Map<String, int>> getDataStats() async {
    final data = await loadAllData();
    return {
      'products': (data['products'] as List).length,
      'categories': (data['categories'] as List).length,
      'invoices': (data['invoices'] as List).length,
      'tasks': (data['tasks'] as List).length,
      'colis': (data['colis'] as List).length,
    };
  }

  /// Forcer la synchronisation des données depuis les services
  Future<bool> syncFromServices() async {
    try {
      // Cette méthode sera appelée pour synchroniser les données
      // depuis les différents services vers le stockage persistant
      debugPrint('🔄 Synchronisation des données depuis les services...');
      
      // Les services individuels appelleront saveAllData() avec leurs données
      return true;
    } catch (e) {
      debugPrint('❌ Erreur lors de la synchronisation: $e');
      return false;
    }
  }
}
