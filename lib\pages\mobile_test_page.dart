import 'package:flutter/material.dart';
import '../services/mobile_optimization_service.dart';
import '../services/invoice_service.dart';
import '../models/invoice.dart';

class MobileTestPage extends StatefulWidget {
  const MobileTestPage({super.key});

  @override
  State<MobileTestPage> createState() => _MobileTestPageState();
}

class _MobileTestPageState extends State<MobileTestPage> {
  bool _isRunning = false;
  final List<String> _logs = [];
  Map<String, dynamic>? _deviceInfo;

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
  }

  Future<void> _loadDeviceInfo() async {
    final mobileService = MobileOptimizationService.instance;

    final info = {
      'isMobile': mobileService.isMobile,
      'isLowEndDevice': await mobileService.isLowEndDevice,
      'optimalBatchSize': await mobileService.getOptimalBatchSize(),
      'optimalDelay': (await mobileService.getOptimalDelay()).inMilliseconds,
      'optimizationInfo': await mobileService.getOptimizationInfo(),
    };

    setState(() {
      _deviceInfo = info;
    });
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testInvoiceSave() async {
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    _addLog('🚀 Début du test de sauvegarde facture mobile');

    try {
      final invoiceService = InvoiceService();

      // Créer une facture de test
      final testInvoice = Invoice(
        id: 'test_mobile_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Test Mobile Client',
        clientNumber: '123456789',
        products: 'Test Product',
        items: [],
        deliveryLocation: 'Test Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      _addLog('📝 Facture de test créée');

      // Test de sauvegarde avec mesure de performance
      final stopwatch = Stopwatch()..start();

      final savedInvoice = await invoiceService.addInvoice(testInvoice);

      stopwatch.stop();

      _addLog('✅ Facture sauvegardée avec succès');
      _addLog('⏱️ Temps de sauvegarde: ${stopwatch.elapsedMilliseconds}ms');
      _addLog('🆔 ID facture: ${savedInvoice.id}');

      // Test de chargement
      _addLog('🔄 Test de chargement des factures...');
      final loadStopwatch = Stopwatch()..start();

      final invoices = await InvoiceService.loadInvoices();

      loadStopwatch.stop();

      _addLog('📊 ${invoices.length} factures chargées');
      _addLog('⏱️ Temps de chargement: ${loadStopwatch.elapsedMilliseconds}ms');

      // Analyser les performances
      if (stopwatch.elapsedMilliseconds > 1000) {
        _addLog('⚠️ Sauvegarde lente (>1s) - Optimisation nécessaire');
      } else if (stopwatch.elapsedMilliseconds > 500) {
        _addLog('🟡 Sauvegarde modérée (>500ms)');
      } else {
        _addLog('🟢 Sauvegarde rapide (<500ms)');
      }
    } catch (e) {
      _addLog('❌ Erreur: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testMultipleInvoices() async {
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    _addLog('🚀 Test de sauvegarde multiple (stress test)');

    try {
      final invoiceService = InvoiceService();
      const numberOfInvoices = 5; // Test avec 5 factures

      final totalStopwatch = Stopwatch()..start();

      for (int i = 0; i < numberOfInvoices; i++) {
        _addLog('📝 Création facture ${i + 1}/$numberOfInvoices');

        final testInvoice = Invoice(
          id: 'test_multi_${DateTime.now().millisecondsSinceEpoch}_$i',
          clientName: 'Test Client $i',
          clientNumber: '12345678$i',
          products: 'Test Product $i',
          items: [],
          deliveryLocation: 'Test Location $i',
          deliveryPrice: 10.0,
          advance: 0.0,
          subtotal: 100.0 + i,
          total: 110.0 + i,
          status: InvoiceStatus.enAttente,
          createdAt: DateTime.now(),
          type: InvoiceType.normale,
        );

        final stopwatch = Stopwatch()..start();
        await invoiceService.addInvoice(testInvoice);
        stopwatch.stop();

        _addLog(
          '✅ Facture ${i + 1} sauvée en ${stopwatch.elapsedMilliseconds}ms',
        );

        // Petite pause pour éviter de surcharger
        await Future.delayed(const Duration(milliseconds: 100));
      }

      totalStopwatch.stop();

      _addLog('🎉 Test terminé!');
      _addLog('⏱️ Temps total: ${totalStopwatch.elapsedMilliseconds}ms');
      _addLog(
        '📊 Moyenne: ${(totalStopwatch.elapsedMilliseconds / numberOfInvoices).round()}ms/facture',
      );
    } catch (e) {
      _addLog('❌ Erreur: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Mobile'),
        backgroundColor: Colors.green[900],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informations de l'appareil
            if (_deviceInfo != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.phone_android, color: Colors.green[700]),
                          const SizedBox(width: 8),
                          Text(
                            'Informations Appareil',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(
                        'Mobile',
                        _deviceInfo!['isMobile'].toString(),
                      ),
                      _buildInfoRow(
                        'Appareil faible',
                        _deviceInfo!['isLowEndDevice'].toString(),
                      ),
                      _buildInfoRow(
                        'Taille lot optimale',
                        _deviceInfo!['optimalBatchSize'].toString(),
                      ),
                      _buildInfoRow(
                        'Délai optimal',
                        '${_deviceInfo!['optimalDelay']}ms',
                      ),
                      _buildInfoRow('Mode', _deviceInfo!['optimizationInfo']),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Boutons de test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.speed, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        Text(
                          'Tests de Performance',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRunning ? null : _testInvoiceSave,
                            icon: const Icon(Icons.receipt),
                            label: const Text('Test Facture'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed:
                                _isRunning ? null : _testMultipleInvoices,
                            icon: const Icon(Icons.speed),
                            label: const Text('Stress Test'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Logs
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.terminal, color: Colors.grey[700]),
                          const SizedBox(width: 8),
                          Text(
                            'Logs de Test',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Spacer(),
                          if (_isRunning)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child:
                              _logs.isEmpty
                                  ? const Text(
                                    'Aucun test lancé. Cliquez sur un bouton pour commencer.',
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                  : ListView.builder(
                                    itemCount: _logs.length,
                                    itemBuilder: (context, index) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 2,
                                        ),
                                        child: Text(
                                          _logs[index],
                                          style: const TextStyle(
                                            fontFamily: 'monospace',
                                            fontSize: 12,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
